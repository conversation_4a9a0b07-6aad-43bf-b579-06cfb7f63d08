"""
ANALYSEUR D'ENTROPIE BACCARAT - INDEX5 (Julia Version)
=====================================================

Programme d'analyse de l'évolution de l'entropie pour l'INDEX5 au cours d'une partie de baccarat.
Basé sur les formules d'entropie de Shannon et implémenté selon les bonnes pratiques Julia.

Architecture modulaire avec types paramétriques et dispatch multiple.
"""

using JSON
using Statistics
using LinearAlgebra
using Printf

# Imports conditionnels pour les graphiques
const GRAPHIQUES_DISPONIBLES = Ref(false)

# Essayer d'importer les packages de visualisation
try
    using Plots
    using StatsPlots
    using PlotlyJS
    using MultivariateStats
    GRAPHIQUES_DISPONIBLES[] = true
    println("✅ Packages de visualisation chargés avec succès")
catch e
    GRAPHIQUES_DISPONIBLES[] = false
    println("⚠️ Packages de visualisation non disponibles: $e")
end

function verifier_packages_graphiques()
    return GRAPHIQUES_DISPONIBLES[]
end

# ═══════════════════════════════════════════════════════════════════════════════
# TYPES ET STRUCTURES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    GraphiqueEntropique{T<:AbstractFloat}

Générateur de graphiques complets pour l'analyse entropique.
Crée tous les graphiques révélateurs avec les 48 métriques.

# Fields
- `output_dir::String`: Dossier de sortie pour les graphiques
- `partie_name::String`: Nom de la partie analysée
- `resolution::Tuple{Int,Int}`: Résolution des graphiques (défaut: (1200,800))
- `dpi::Int`: DPI pour la qualité (défaut: 300)
"""
mutable struct GraphiqueEntropique{T<:AbstractFloat}
    output_dir::String
    partie_name::String
    resolution::Tuple{Int,Int}
    dpi::Int

    function GraphiqueEntropique{T}(partie_name::String) where T<:AbstractFloat
        output_dir = "graphiques_$(partie_name)"
        # Créer le dossier s'il n'existe pas
        if !isdir(output_dir)
            mkdir(output_dir)
        end
        new{T}(output_dir, partie_name, (1200, 800), 300)
    end
end

# Constructeur par défaut
GraphiqueEntropique(partie_name::String) = GraphiqueEntropique{Float64}(partie_name)

"""
    EntropyAnalyzer{T<:AbstractFloat}

Analyseur d'entropie pour le baccarat basé sur les formules de Shannon.

# Fields
- `base::T`: Base pour les calculs logarithmiques (défaut: 2.0 pour bits)
- `epsilon::T`: Petite valeur pour éviter log(0) (défaut: 1e-12)
- `theoretical_probs::Dict{String,T}`: Probabilités théoriques INDEX5

# Examples
```julia
analyzer = EntropyAnalyzer{Float64}()
entropy = calculate_shannon_entropy(analyzer, [0.5, 0.5])
```
"""
struct EntropyAnalyzer{T<:AbstractFloat}
    base::T
    epsilon::T
    theoretical_probs::Dict{String,T}
    
    function EntropyAnalyzer{T}(
        base::T = T(2.0),
        epsilon::T = T(1e-12)
    ) where T<:AbstractFloat
        if base <= 1
            throw(ArgumentError("Base must be > 1, got $base"))
        end
        if epsilon <= 0
            throw(ArgumentError("Epsilon must be > 0, got $epsilon"))
        end
        
        # Probabilités théoriques INDEX5 normalisées
        theoretical_probs = Dict{String,T}(
            "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
            "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
            "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
            "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
            "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
            "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
            "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
            "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
            "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
        )
        
        new{T}(base, epsilon, theoretical_probs)
    end
end

# Constructeur de convenance
EntropyAnalyzer(args...) = EntropyAnalyzer{Float64}(args...)

"""
    EntropyMetrics{T<:AbstractFloat}

Structure pour stocker les métriques d'entropie calculées.
"""
struct EntropyMetrics{T<:AbstractFloat}
    position::Int
    sequence_length::Int
    unique_values::Int
    simple_entropy::T
    simple_entropy_theoretical::T
    conditional_entropy::T
    metric_entropy::T
    entropy_rate::T
    entropy_aep_observed::T        # EGobs (Entropie Générale Observée)
    conf_eg::T                     # ConfEG = |EntropG - EGobs|
    struct_eg::T                   # StructEG = EntropG/EGobs
    block_entropies::Vector{T}
end

"""
    PredictiveDifferentials{T<:AbstractFloat}

Structure pour les différentiels prédictifs selon les définitions exactes.
Correspondances:
- DiffC = DiffCond (Différentiel Entropie Conditionnelle)
- DiffT5 = DiffT5 (Différentiel T5)
- DiffDivEG = DiffDivEntropG (Différentiel Diversité Entropique)
- DiffEG = DiffEG (Différentiel Entropie Générale)
- DiffEGobs = DiffEGobs (Différentiel Entropie Générale Observée)
- DiffSEG = DiffSEG (Différentiel Structure Entropique)
- DiffCEG = DiffCEG (Différentiel Conformité Entropique)
- SCORE = (DiffC + DiffEG) / (DiffT5 + DiffDivEG)
"""
struct PredictiveDifferentials{T<:AbstractFloat}
    diff_cond::T          # DiffC = DiffCond
    diff_taux::T          # DiffT5 = DiffT5
    diff_div_entrop_g::T  # DiffDivEG = DiffDivEntropG
    diff_entrop_g::T      # DiffEG = DiffEG
    diff_egobs::T         # DiffEGobs = DiffEGobs
    diff_seg::T           # DiffSEG = DiffSEG
    diff_ceg::T           # DiffCEG = DiffCEG
    score::T              # SCORE = (DiffC + DiffEG) / (DiffT5 + DiffDivEG)
end

"""
    FormulasObserved{T<:AbstractFloat}

Classe contenant les 12 formules OBS (observées) pour fenêtres croissantes.
Utilise les fréquences réelles observées dans la séquence de jeu.

Basé sur le fichier Formules1.txt - 12 formules × version observée.
Chaque formule calcule une métrique d'entropie pour la main n en utilisant
les patterns réellement observés dans la séquence jusqu'à cette main.

# Fields
- `base::T`: Base pour les calculs logarithmiques (défaut: 2.0 pour bits)
- `epsilon::T`: Petite valeur pour éviter log(0) (défaut: 1e-12)

# Formules implémentées (version observée)
1. Shannon Jointe : H_obs(X₁, X₂, ..., Xₙ)
2. AEP : H_AEP_obs(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_obs(xᵢ)
3. Taux d'Entropie : H_obs(Ξ) = lim_{n→∞} (1/n) H_obs(X₁, ..., Xₙ)
4. Entropie Métrique : h_μ_obs(T) = sup{h_μ_obs(T, α)}
5. Conditionnelle Cumulative : H_obs(Xₙ|X₁, ..., Xₙ₋₁)
6. Divergence KL : D_KL_obs_theo(P_n||Q_n)
7. Information Mutuelle : I_obs(X₁ⁿ; Y₁ⁿ)
8. Entropie Croisée : H_cross_obs_theo(P_n, Q_n)
9. Entropie Topologique : h_top_obs(f)
10. Block Cumulative : H_n_obs = H_obs(X₁, ..., Xₙ)
11. Conditionnelle Décroissante : u_n_obs = H_obs(Xₙ|X₁, ..., Xₙ₋₁)
12. Théorème AEP : lim_{n→∞} -(1/n) log₂ p_obs(X₁ⁿ) = H_obs(Ξ)
"""
struct FormulasObserved{T<:AbstractFloat}
    base::T
    epsilon::T

    function FormulasObserved{T}(
        base::T = T(2.0),
        epsilon::T = T(1e-12)
    ) where T<:AbstractFloat
        if base <= 1
            throw(ArgumentError("Base must be > 1, got $base"))
        end
        if epsilon <= 0
            throw(ArgumentError("Epsilon must be > 0, got $epsilon"))
        end

        new{T}(base, epsilon)
    end
end

# Constructeur de convenance
FormulasObserved(args...) = FormulasObserved{Float64}(args...)

"""
    FormulasTheoretical{T<:AbstractFloat}

Classe contenant les 12 formules THEO (théoriques) pour fenêtres croissantes.
Utilise les probabilités théoriques INDEX5 du modèle de référence.

Basé sur le fichier Formules1.txt - 12 formules × version théorique.
Chaque formule calcule une métrique d'entropie pour la main n en utilisant
les probabilités théoriques INDEX5 définies dans le modèle.

# Fields
- `base::T`: Base pour les calculs logarithmiques (défaut: 2.0 pour bits)
- `epsilon::T`: Petite valeur pour éviter log(0) (défaut: 1e-12)
- `theoretical_probs::Dict{String,T}`: Probabilités théoriques INDEX5

# Formules implémentées (version théorique)
1. Shannon Jointe : H_theo(X₁, X₂, ..., Xₙ)
2. AEP : H_AEP_theo(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_theo(xᵢ)
3. Taux d'Entropie : H_theo(Ξ) = lim_{n→∞} (1/n) H_theo(X₁, ..., Xₙ)
4. Entropie Métrique : h_μ_theo(T) = sup{h_μ_theo(T, α)}
5. Conditionnelle Cumulative : H_theo(Xₙ|X₁, ..., Xₙ₋₁)
6. Divergence KL : D_KL_theo_unif(P_n||U_n)
7. Information Mutuelle : I_theo(X₁ⁿ; Y₁ⁿ)
8. Entropie Croisée : H_cross_theo_unif(P_n, U_n)
9. Entropie Topologique : h_top_theo(f)
10. Block Cumulative : H_n_theo = H_theo(X₁, ..., Xₙ)
11. Conditionnelle Décroissante : u_n_theo = H_theo(Xₙ|X₁, ..., Xₙ₋₁)
12. Théorème AEP : lim_{n→∞} -(1/n) log₂ p_theo(X₁ⁿ) = H_theo(Ξ)
"""
struct FormulasTheoretical{T<:AbstractFloat}
    base::T
    epsilon::T
    theoretical_probs::Dict{String,T}

    function FormulasTheoretical{T}(
        base::T = T(2.0),
        epsilon::T = T(1e-12)
    ) where T<:AbstractFloat
        if base <= 1
            throw(ArgumentError("Base must be > 1, got $base"))
        end
        if epsilon <= 0
            throw(ArgumentError("Epsilon must be > 0, got $epsilon"))
        end

        # Probabilités théoriques INDEX5 normalisées (identiques à EntropyAnalyzer)
        theoretical_probs = Dict{String,T}(
            "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
            "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
            "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
            "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
            "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
            "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
            "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
            "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
            "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
        )

        new{T}(base, epsilon, theoretical_probs)
    end
end

# Constructeur de convenance
FormulasTheoretical(args...) = FormulasTheoretical{Float64}(args...)

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS UTILITAIRES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    safe_log(x::T, base::T, epsilon::T) where T<:AbstractFloat -> T

Calcul sécurisé du logarithme avec gestion de log(0).
"""
function safe_log(x::T, base::T, epsilon::T) where T<:AbstractFloat
    if x <= zero(T)
        x = epsilon
    end
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return log(x) / log(base)
    return zero(T)  # Valeur par défaut temporaire
end

"""
    validate_probabilities(probs::Vector{T}) where T<:AbstractFloat -> Vector{T}

Valide et normalise un vecteur de probabilités.
"""
function validate_probabilities(probs::Vector{T}) where T<:AbstractFloat
    if isempty(probs)
        throw(ArgumentError("Probability vector cannot be empty"))
    end

    # Convertir en Float64 si nécessaire et filtrer les valeurs négatives
    clean_probs = max.(probs, zero(T))

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # Normaliser
    # total = sum(clean_probs)
    # if total ≈ zero(T)
    #     throw(ArgumentError("All probabilities are zero"))
    # end
    # return clean_probs ./ total

    # Valeur par défaut temporaire
    return clean_probs
end

# ═══════════════════════════════════════════════════════════════════════════════
# IMPLÉMENTATION DES FORMULES OBSERVÉES (FormulasObserved)
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule1A_shannon_jointe_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 1A : Entropie de Shannon Jointe (VERSION OBSERVÉE)
Formule : H_obs(X₁, X₂, ..., Xₙ) = -∑ p_obs(x₁,...,xₙ) log₂ p_obs(x₁,...,xₙ)

Calcule l'entropie jointe observée pour la fenêtre croissante de la main 1 à la main n.
Utilise les fréquences réellement observées dans la séquence.
"""
function calculer_formule1A_shannon_jointe_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences de chaque combinaison
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie de Shannon observée
    entropy = zero(T)
    total = length(subsequence)

    for count in values(counts)
        if count > 0
            p_obs = T(count) / T(total)
            entropy -= p_obs * (log(p_obs) / log(formulas.base))
        end
    end

    return entropy
end

"""
    calculer_formule2A_aep_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 2A : Entropie par Symbole AEP (VERSION OBSERVÉE)
Formule : H_AEP_obs(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_obs(xᵢ)

Calcule l'entropie moyenne par symbole selon l'AEP avec probabilités observées.
"""
function calculer_formule2A_aep_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences pour calculer p_obs
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer la somme des logarithmes avec probabilités observées
    # FORMULE CORRIGÉE : H_AEP_obs(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_obs(xᵢ)
    total_log_prob = zero(T)
    for value in subsequence
        p_obs = T(counts[value]) / T(n)
        if p_obs > zero(T)
            # log(p_obs) est négatif, donc on soustrait pour obtenir une valeur positive
            total_log_prob -= log(p_obs) / log(formulas.base)
        else
            # Gestion du cas p_obs = 0 avec epsilon
            total_log_prob -= log(formulas.epsilon) / log(formulas.base)
        end
    end

    # Appliquer la formule AEP observée : moyenne des -log₂(p_obs)
    return total_log_prob / n
end

"""
    calculer_formule3A_taux_entropie_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 3A : Taux d'Entropie (VERSION OBSERVÉE)
Formule : H_obs(Ξ) = lim_{n→∞} (1/n) H_obs(X₁, X₂, ..., Xₙ)

Estime le taux d'entropie observé comme limite asymptotique.
"""
function calculer_formule3A_taux_entropie_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Calculer l'entropie jointe observée
    h_joint = calculer_formule1A_shannon_jointe_obs(formulas, sequence, n)

    # Estimer le taux d'entropie : (1/n) × H_obs(X₁, ..., Xₙ)
    return h_joint / n
end

"""
    calculer_formule4A_entropie_metrique_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 4A : Entropie Métrique Kolmogorov-Sinai (VERSION OBSERVÉE)
Formule : h_μ_obs(T) = sup{h_μ_obs(T, α) : α partition finie}

Approximation pratique de l'entropie métrique observée.
"""
function calculer_formule4A_entropie_metrique_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Approximation : h_metric ≈ H(n)/n pour la partition naturelle INDEX5
    h_joint = calculer_formule1A_shannon_jointe_obs(formulas, sequence, n)
    return h_joint / n
end

"""
    calculer_formule5A_conditionnelle_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 5A : Entropie Conditionnelle Cumulative (VERSION OBSERVÉE)
Formule : H_obs(Xₙ|X₁, ..., Xₙ₋₁) = H_obs(X₁, ..., Xₙ) - H_obs(X₁, ..., Xₙ₋₁)

Calcule l'information observée apportée par le n-ème symbole.
"""
function calculer_formule5A_conditionnelle_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 1 || n > length(sequence)
        return zero(T)
    end

    # H_obs(X₁, ..., Xₙ)
    h_n = calculer_formule1A_shannon_jointe_obs(formulas, sequence, n)

    # H_obs(X₁, ..., Xₙ₋₁)
    h_n_minus_1 = calculer_formule1A_shannon_jointe_obs(formulas, sequence, n-1)

    # Entropie conditionnelle
    return h_n - h_n_minus_1
end

"""
    calculer_formule6A_divergence_kl_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 6A : Entropie Relative - Divergence KL (VERSION OBSERVÉE vs THÉORIQUE)
Formule : D_KL_obs_theo(P_n||Q_n) = ∑ᵢ₌₁ⁿ p_obs(xᵢ) log₂(p_obs(xᵢ)/p_theo(xᵢ))

CORRECTION SELON Formules1.txt :
- Compare distribution OBSERVÉE vs THÉORIQUE INDEX5 (pas vs uniforme)
- Utilise les probabilités INDEX5 comme référence théorique
"""
function calculer_formule6A_divergence_kl_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # CORRECTION : Utiliser les probabilités INDEX5 théoriques (pas uniforme)
    # Probabilités théoriques INDEX5 (identiques à FormulasTheoretical)
    theoretical_probs = Dict{String,T}(
        "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
        "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
        "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
        "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
        "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
        "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
        "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
        "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
        "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
    )

    # Calculer la divergence KL observée vs théorique INDEX5
    divergence = zero(T)
    total = length(subsequence)

    for (value, count) in counts
        p_obs = T(count) / T(total)
        if p_obs > zero(T)
            p_theo = get(theoretical_probs, value, formulas.epsilon)
            if p_theo > zero(T)
                divergence += p_obs * (log(p_obs/p_theo) / log(formulas.base))
            end
        end
    end

    return divergence
end

"""
    calculer_formule7A_information_mutuelle_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 7A : Information Mutuelle Cumulative (VERSION OBSERVÉE)
Formule EXACTE selon cours d'entropie : I(X;Y) = ∑∑ p(x,y) log₂ (p(x,y)/(p(x)p(y)))

CORRECTION SELON COURS D'ENTROPIE :
- Mesure la dépendance temporelle entre X_t et X_{t+1} (auto-corrélation lag-1)
- Utilise les probabilités jointes observées p(x_t, x_{t+1})
- Applique la formule exacte de l'information mutuelle
"""
function calculer_formule7A_information_mutuelle_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 2 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # CORRECTION : Calculer l'information mutuelle entre X_t et X_{t+1} (auto-corrélation lag-1)
    # Créer les paires (x_t, x_{t+1}) pour mesurer la dépendance temporelle
    pairs = Tuple{String, String}[]
    for i in 1:(length(subsequence)-1)
        push!(pairs, (subsequence[i], subsequence[i+1]))
    end

    if isempty(pairs)
        return zero(T)
    end

    # Compter les occurrences des paires jointes p(x_t, x_{t+1})
    joint_counts = Dict{Tuple{String, String}, Int}()
    for pair in pairs
        joint_counts[pair] = get(joint_counts, pair, 0) + 1
    end

    # Compter les occurrences marginales p(x_t) et p(x_{t+1})
    x_counts = Dict{String, Int}()
    y_counts = Dict{String, Int}()
    for (x, y) in pairs
        x_counts[x] = get(x_counts, x, 0) + 1
        y_counts[y] = get(y_counts, y, 0) + 1
    end

    # Calculer l'information mutuelle : I(X;Y) = ∑∑ p(x,y) log₂ (p(x,y)/(p(x)p(y)))
    mutual_info = zero(T)
    total_pairs = T(length(pairs))

    for ((x, y), joint_count) in joint_counts
        p_xy = T(joint_count) / total_pairs
        p_x = T(x_counts[x]) / total_pairs
        p_y = T(y_counts[y]) / total_pairs

        if p_xy > zero(T) && p_x > zero(T) && p_y > zero(T)
            # I(X;Y) += p(x,y) * log₂(p(x,y) / (p(x) * p(y)))
            mutual_info += p_xy * (log(p_xy / (p_x * p_y)) / log(formulas.base))
        end
    end

    return mutual_info
end

"""
    calculer_formule8A_entropie_croisee_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 8A : Entropie Croisée Cumulative (VERSION OBSERVÉE vs THÉORIQUE)
Formule : H_cross_obs_theo(P_n, Q_n) = -∑ᵢ₌₁ⁿ p_obs(xᵢ) log₂ p_theo(xᵢ)

CORRECTION SELON Formules1.txt :
- Coût d'encodage des données observées avec le modèle INDEX5
- Utilise les probabilités INDEX5 comme distribution de référence
"""
function calculer_formule8A_entropie_croisee_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # CORRECTION : Utiliser les probabilités INDEX5 théoriques
    theoretical_probs = Dict{String,T}(
        "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
        "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
        "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
        "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
        "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
        "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
        "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
        "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
        "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
    )

    # Calculer l'entropie croisée observée vs théorique INDEX5
    cross_entropy = zero(T)
    total = length(subsequence)

    for (value, count) in counts
        p_obs = T(count) / T(total)
        if p_obs > zero(T)
            p_theo = get(theoretical_probs, value, formulas.epsilon)
            if p_theo > zero(T)
                cross_entropy -= p_obs * (log(p_theo) / log(formulas.base))
            else
                cross_entropy -= p_obs * (log(formulas.epsilon) / log(formulas.base))
            end
        end
    end

    return cross_entropy
end

"""
    calculer_formule9A_entropie_topologique_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 9A : Entropie Topologique Cumulative (VERSION OBSERVÉE)
Formule : h_top_obs(f) = lim_{n→∞} (1/n) log₂ |{orbites périodiques de période ≤ n}|

Approximation pratique basée sur la diversité des patterns observés.
"""
function calculer_formule9A_entropie_topologique_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter le nombre de patterns distincts (approximation de la complexité topologique)
    unique_patterns = Set(subsequence)
    num_patterns = length(unique_patterns)

    # Approximation : h_top ≈ (1/n) log₂(nombre_patterns_distincts)
    if num_patterns > 0
        return (log(T(num_patterns)) / log(formulas.base)) / n
    else
        return zero(T)
    end
end

"""
    calculer_formule10A_block_cumulative_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 10A : Entropie de Block Cumulative (VERSION OBSERVÉE)
Formule : H_n_obs = H_obs(X₁, X₂, ..., Xₙ)

Identique à l'entropie jointe de Shannon - incluse pour cohérence avec Formules1.txt.
"""
function calculer_formule10A_block_cumulative_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    # Cette formule est identique à la formule 1A (Shannon Jointe)
    return calculer_formule1A_shannon_jointe_obs(formulas, sequence, n)
end

"""
    calculer_formule11A_conditionnelle_decroissante_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 11A : Entropie Conditionnelle Décroissante (VERSION OBSERVÉE)
Formule : u_n_obs = H_obs(Xₙ|X₁, X₂, ..., Xₙ₋₁)

Identique à la formule 5A (Conditionnelle Cumulative) - incluse pour cohérence.
"""
function calculer_formule11A_conditionnelle_decroissante_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    # Cette formule est identique à la formule 5A (Conditionnelle Cumulative)
    return calculer_formule5A_conditionnelle_obs(formulas, sequence, n)
end

"""
    calculer_formule12A_theoreme_aep_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 12A : Théorème AEP Shannon-McMillan-Breiman (VERSION OBSERVÉE)
Formule : lim_{n→∞} -(1/n) log₂ p_obs(X₁ⁿ) = H_obs(Ξ)

Convergence vers le taux d'entropie observé selon le théorème AEP.
"""
function calculer_formule12A_theoreme_aep_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Calculer -(1/n) log₂ p_obs(X₁ⁿ) selon le théorème AEP
    # Ceci est équivalent à la formule 2A (AEP observée)
    return calculer_formule2A_aep_obs(formulas, sequence, n)
end

# ═══════════════════════════════════════════════════════════════════════════════
# IMPLÉMENTATION DES FORMULES THÉORIQUES (FormulasTheoretical)
# ═══════════════════════════════════════════════════════════════════════════════

# ═══════════════════════════════════════════════════════════════════════════════
# CLASSE POUR LES DIFFÉRENTIELS DES MÉTRIQUES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    DifferentialMetrics{T}

Classe pour calculer les différentiels de toutes les métriques d'entropie.
Calcule |métrique(n) - métrique(n-1)| pour chaque métrique de la main 6 à 60.
"""
mutable struct DifferentialMetrics{T<:AbstractFloat}
    # Différentiels des formules théoriques (12)
    diff_shannon_t::Vector{T}
    diff_aep_t::Vector{T}
    diff_taux_t::Vector{T}
    diff_metric_t::Vector{T}
    diff_cond_t::Vector{T}
    diff_divkl_t::Vector{T}
    diff_infomut_t::Vector{T}
    diff_cross_t::Vector{T}
    diff_topo_t::Vector{T}
    diff_block_t::Vector{T}
    diff_conddec_t::Vector{T}
    diff_theoaep_t::Vector{T}

    # Différentiels des formules observées (12)
    diff_shannon_o::Vector{T}
    diff_aep_o::Vector{T}
    diff_taux_o::Vector{T}
    diff_metric_o::Vector{T}
    diff_cond_o::Vector{T}
    diff_divkl_o::Vector{T}
    diff_infomut_o::Vector{T}
    diff_cross_o::Vector{T}
    diff_topo_o::Vector{T}
    diff_block_o::Vector{T}
    diff_conddec_o::Vector{T}
    diff_theoaep_o::Vector{T}

    # Métadonnées
    start_main::Int  # Main de début (6)
    end_main::Int    # Main de fin (60)
    base::T          # Base logarithmique

    function DifferentialMetrics{T}(start_main::Int, end_main::Int, base::T) where T<:AbstractFloat
        num_diffs = end_main - start_main + 1  # Nombre de différentiels à calculer

        new{T}(
            # Théoriques
            Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs),
            Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs),
            Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs),
            Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs),
            # Observées
            Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs),
            Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs),
            Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs),
            Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs), Vector{T}(undef, num_diffs),
            # Métadonnées
            start_main, end_main, base
        )
    end
end

"""
    calculer_differentiels!(diff_metrics::DifferentialMetrics{T}, formulas_obs::FormulasObserved{T}, formulas_theo::FormulasTheoretical{T}, sequence::Vector{String}) where T

Calcule tous les différentiels des 24 métriques de la main 2 à 60.
Formule : Diff_métrique(n) = |métrique(n) - métrique(n-1)|
"""
function calculer_differentiels!(
    diff_metrics::DifferentialMetrics{T},
    formulas_obs::FormulasObserved{T},
    formulas_theo::FormulasTheoretical{T},
    sequence::Vector{String}
) where T<:AbstractFloat

    start_main = diff_metrics.start_main
    end_main = diff_metrics.end_main

    # Calculer les différentiels pour chaque main de start_main à end_main
    for (idx, n) in enumerate(start_main:end_main)
        if n <= 1 || n > length(sequence)
            # Remplir avec des zéros si hors limites
            fill_zeros_at_index!(diff_metrics, idx)
            continue
        end

        # Calculer les métriques pour la main n et n-1
        metrics_n = calculer_toutes_metriques(formulas_obs, formulas_theo, sequence, n)
        metrics_n_minus_1 = calculer_toutes_metriques(formulas_obs, formulas_theo, sequence, n-1)

        # Calculer les différentiels absolus |métrique(n) - métrique(n-1)|
        calculer_differentiels_pour_main!(diff_metrics, metrics_n, metrics_n_minus_1, idx)
    end
end

"""
    calculer_toutes_metriques(formulas_obs::FormulasObserved{T}, formulas_theo::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T

Calcule toutes les 24 métriques pour une main donnée.
Retourne un tuple avec toutes les valeurs.
"""
function calculer_toutes_metriques(
    formulas_obs::FormulasObserved{T},
    formulas_theo::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat

    # Formules théoriques (12)
    shannon_t = calculer_formule1B_shannon_jointe_theo(formulas_theo, sequence, n)
    aep_t = calculer_formule2B_aep_theo(formulas_theo, sequence, n)
    taux_t = calculer_formule3B_taux_entropie_theo(formulas_theo, sequence, n)
    metric_t = calculer_formule4B_entropie_metrique_theo(formulas_theo, sequence, n)
    cond_t = calculer_formule5B_conditionnelle_theo(formulas_theo, sequence, n)
    divkl_t = calculer_formule6B_divergence_kl_theo(formulas_theo, sequence, n)
    infomut_t = calculer_formule7B_information_mutuelle_theo(formulas_theo, sequence, n)
    cross_t = calculer_formule8B_entropie_croisee_theo(formulas_theo, sequence, n)
    topo_t = calculer_formule9B_entropie_topologique_theo(formulas_theo, sequence, n)
    block_t = calculer_formule10B_block_cumulative_theo(formulas_theo, sequence, n)
    conddec_t = calculer_formule11B_conditionnelle_decroissante_theo(formulas_theo, sequence, n)
    theoaep_t = calculer_formule12B_theoreme_aep_theo(formulas_theo, sequence, n)

    # Formules observées (12)
    shannon_o = calculer_formule1A_shannon_jointe_obs(formulas_obs, sequence, n)
    aep_o = calculer_formule2A_aep_obs(formulas_obs, sequence, n)
    taux_o = calculer_formule3A_taux_entropie_obs(formulas_obs, sequence, n)
    metric_o = calculer_formule4A_entropie_metrique_obs(formulas_obs, sequence, n)
    cond_o = calculer_formule5A_conditionnelle_obs(formulas_obs, sequence, n)
    divkl_o = calculer_formule6A_divergence_kl_obs(formulas_obs, sequence, n)
    infomut_o = calculer_formule7A_information_mutuelle_obs(formulas_obs, sequence, n)
    cross_o = calculer_formule8A_entropie_croisee_obs(formulas_obs, sequence, n)
    topo_o = calculer_formule9A_entropie_topologique_obs(formulas_obs, sequence, n)
    block_o = calculer_formule10A_block_cumulative_obs(formulas_obs, sequence, n)
    conddec_o = calculer_formule11A_conditionnelle_decroissante_obs(formulas_obs, sequence, n)
    theoaep_o = calculer_formule12A_theoreme_aep_obs(formulas_obs, sequence, n)

    return (
        # Théoriques
        shannon_t, aep_t, taux_t, metric_t, cond_t, divkl_t, infomut_t, cross_t, topo_t, block_t, conddec_t, theoaep_t,
        # Observées
        shannon_o, aep_o, taux_o, metric_o, cond_o, divkl_o, infomut_o, cross_o, topo_o, block_o, conddec_o, theoaep_o
    )
end

"""
    calculer_differentiels_pour_main!(diff_metrics::DifferentialMetrics{T}, metrics_n::Tuple, metrics_n_minus_1::Tuple, idx::Int) where T

Calcule les différentiels absolus pour une main donnée et les stocke dans la structure.
"""
function calculer_differentiels_pour_main!(
    diff_metrics::DifferentialMetrics{T},
    metrics_n::Tuple,
    metrics_n_minus_1::Tuple,
    idx::Int
) where T<:AbstractFloat

    # Extraire les métriques théoriques (12 premières)
    shannon_t_n, aep_t_n, taux_t_n, metric_t_n, cond_t_n, divkl_t_n, infomut_t_n, cross_t_n, topo_t_n, block_t_n, conddec_t_n, theoaep_t_n = metrics_n[1:12]
    shannon_t_n_1, aep_t_n_1, taux_t_n_1, metric_t_n_1, cond_t_n_1, divkl_t_n_1, infomut_t_n_1, cross_t_n_1, topo_t_n_1, block_t_n_1, conddec_t_n_1, theoaep_t_n_1 = metrics_n_minus_1[1:12]

    # Extraire les métriques observées (12 dernières)
    shannon_o_n, aep_o_n, taux_o_n, metric_o_n, cond_o_n, divkl_o_n, infomut_o_n, cross_o_n, topo_o_n, block_o_n, conddec_o_n, theoaep_o_n = metrics_n[13:24]
    shannon_o_n_1, aep_o_n_1, taux_o_n_1, metric_o_n_1, cond_o_n_1, divkl_o_n_1, infomut_o_n_1, cross_o_n_1, topo_o_n_1, block_o_n_1, conddec_o_n_1, theoaep_o_n_1 = metrics_n_minus_1[13:24]

    # Calculer les différentiels absolus pour les métriques théoriques
    diff_metrics.diff_shannon_t[idx] = abs(shannon_t_n - shannon_t_n_1)
    diff_metrics.diff_aep_t[idx] = abs(aep_t_n - aep_t_n_1)
    diff_metrics.diff_taux_t[idx] = abs(taux_t_n - taux_t_n_1)
    diff_metrics.diff_metric_t[idx] = abs(metric_t_n - metric_t_n_1)
    diff_metrics.diff_cond_t[idx] = abs(cond_t_n - cond_t_n_1)
    diff_metrics.diff_divkl_t[idx] = abs(divkl_t_n - divkl_t_n_1)
    diff_metrics.diff_infomut_t[idx] = abs(infomut_t_n - infomut_t_n_1)
    diff_metrics.diff_cross_t[idx] = abs(cross_t_n - cross_t_n_1)
    diff_metrics.diff_topo_t[idx] = abs(topo_t_n - topo_t_n_1)
    diff_metrics.diff_block_t[idx] = abs(block_t_n - block_t_n_1)
    diff_metrics.diff_conddec_t[idx] = abs(conddec_t_n - conddec_t_n_1)
    diff_metrics.diff_theoaep_t[idx] = abs(theoaep_t_n - theoaep_t_n_1)

    # Calculer les différentiels absolus pour les métriques observées
    diff_metrics.diff_shannon_o[idx] = abs(shannon_o_n - shannon_o_n_1)
    diff_metrics.diff_aep_o[idx] = abs(aep_o_n - aep_o_n_1)
    diff_metrics.diff_taux_o[idx] = abs(taux_o_n - taux_o_n_1)
    diff_metrics.diff_metric_o[idx] = abs(metric_o_n - metric_o_n_1)
    diff_metrics.diff_cond_o[idx] = abs(cond_o_n - cond_o_n_1)
    diff_metrics.diff_divkl_o[idx] = abs(divkl_o_n - divkl_o_n_1)
    diff_metrics.diff_infomut_o[idx] = abs(infomut_o_n - infomut_o_n_1)
    diff_metrics.diff_cross_o[idx] = abs(cross_o_n - cross_o_n_1)
    diff_metrics.diff_topo_o[idx] = abs(topo_o_n - topo_o_n_1)
    diff_metrics.diff_block_o[idx] = abs(block_o_n - block_o_n_1)
    diff_metrics.diff_conddec_o[idx] = abs(conddec_o_n - conddec_o_n_1)
    diff_metrics.diff_theoaep_o[idx] = abs(theoaep_o_n - theoaep_o_n_1)
end

"""
    fill_zeros_at_index!(diff_metrics::DifferentialMetrics{T}, idx::Int) where T

Remplit tous les différentiels avec des zéros pour un index donné.
"""
function fill_zeros_at_index!(diff_metrics::DifferentialMetrics{T}, idx::Int) where T<:AbstractFloat
    # Théoriques
    diff_metrics.diff_shannon_t[idx] = zero(T)
    diff_metrics.diff_aep_t[idx] = zero(T)
    diff_metrics.diff_taux_t[idx] = zero(T)
    diff_metrics.diff_metric_t[idx] = zero(T)
    diff_metrics.diff_cond_t[idx] = zero(T)
    diff_metrics.diff_divkl_t[idx] = zero(T)
    diff_metrics.diff_infomut_t[idx] = zero(T)
    diff_metrics.diff_cross_t[idx] = zero(T)
    diff_metrics.diff_topo_t[idx] = zero(T)
    diff_metrics.diff_block_t[idx] = zero(T)
    diff_metrics.diff_conddec_t[idx] = zero(T)
    diff_metrics.diff_theoaep_t[idx] = zero(T)

    # Observées
    diff_metrics.diff_shannon_o[idx] = zero(T)
    diff_metrics.diff_aep_o[idx] = zero(T)
    diff_metrics.diff_taux_o[idx] = zero(T)
    diff_metrics.diff_metric_o[idx] = zero(T)
    diff_metrics.diff_cond_o[idx] = zero(T)
    diff_metrics.diff_divkl_o[idx] = zero(T)
    diff_metrics.diff_infomut_o[idx] = zero(T)
    diff_metrics.diff_cross_o[idx] = zero(T)
    diff_metrics.diff_topo_o[idx] = zero(T)
    diff_metrics.diff_block_o[idx] = zero(T)
    diff_metrics.diff_conddec_o[idx] = zero(T)
    diff_metrics.diff_theoaep_o[idx] = zero(T)
end

"""
    calculer_formule1B_shannon_jointe_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 1B : Entropie de Shannon Jointe (VERSION THÉORIQUE)
Formule : H_theo(X₁, X₂, ..., Xₙ) = -∑ p_theo(x₁,...,xₙ) log₂ p_theo(x₁,...,xₙ)

Calcule l'entropie jointe théorique pour la fenêtre croissante de la main 1 à la main n.
Utilise les probabilités théoriques INDEX5 du modèle.
"""
function calculer_formule1B_shannon_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # CORRECTION : Calculer l'entropie de Shannon de la DISTRIBUTION théorique
    # Compter les occurrences observées dans la séquence
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie de Shannon théorique basée sur les probabilités INDEX5
    entropy = zero(T)
    total = length(subsequence)

    # Pour chaque valeur unique observée, utiliser sa probabilité théorique INDEX5
    for (value, count) in counts
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            # Entropie de Shannon : -∑ p_theo(x) log₂ p_theo(x)
            # Pondérée par la fréquence d'apparition dans la séquence
            weight = T(count) / T(total)
            entropy -= weight * p_theo * (log(p_theo) / log(formulas.base))
        end
    end

    return entropy
end

"""
    calculer_formule2B_aep_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 2B : Entropie par Symbole AEP (VERSION THÉORIQUE)
Formule : H_AEP_theo(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_theo(xᵢ)

Calcule l'entropie moyenne par symbole selon l'AEP avec probabilités théoriques INDEX5.
"""
function calculer_formule2B_aep_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Calculer la somme des logarithmes avec probabilités théoriques
    # FORMULE CORRIGÉE : H_AEP_theo(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_theo(xᵢ)
    total_log_prob = zero(T)
    for value in subsequence
        p_theo = get(formulas.theoretical_probs, value, zero(T))
        if p_theo > zero(T)
            # log(p_theo) est négatif, donc on soustrait pour obtenir une valeur positive
            total_log_prob -= log(p_theo) / log(formulas.base)
        else
            # Gestion du cas p_theo = 0 avec epsilon
            total_log_prob -= log(formulas.epsilon) / log(formulas.base)
        end
    end

    # Appliquer la formule AEP théorique : moyenne des -log₂(p_theo)
    return total_log_prob / n
end

"""
    calculer_formule3B_taux_entropie_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 3B : Taux d'Entropie (VERSION THÉORIQUE)
Formule : H_theo(Ξ) = lim_{n→∞} (1/n) H_theo(X₁, X₂, ..., Xₙ)

Estime le taux d'entropie théorique comme limite asymptotique.
"""
function calculer_formule3B_taux_entropie_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Calculer l'entropie jointe théorique
    h_joint = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)

    # Estimer le taux d'entropie : (1/n) × H_theo(X₁, ..., Xₙ)
    return h_joint / n
end

"""
    calculer_formule4B_entropie_metrique_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 4B : Entropie Métrique Kolmogorov-Sinai (VERSION THÉORIQUE)
Formule : h_μ_theo(T) = sup{h_μ_theo(T, α) : α partition finie}

Approximation pratique de l'entropie métrique théorique.
"""
function calculer_formule4B_entropie_metrique_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Approximation : h_metric ≈ H(n)/n pour la partition naturelle INDEX5
    h_joint = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)
    return h_joint / n
end

"""
    calculer_formule5B_conditionnelle_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 5B : Entropie Conditionnelle Cumulative (VERSION THÉORIQUE)
Formule : H_theo(Xₙ|X₁, ..., Xₙ₋₁) = H_theo(X₁, ..., Xₙ) - H_theo(X₁, ..., Xₙ₋₁)

Calcule l'information théorique apportée par le n-ème symbole.
"""
function calculer_formule5B_conditionnelle_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 1 || n > length(sequence)
        return zero(T)
    end

    # H_theo(X₁, ..., Xₙ)
    h_n = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)

    # H_theo(X₁, ..., Xₙ₋₁)
    h_n_minus_1 = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n-1)

    # Entropie conditionnelle
    return h_n - h_n_minus_1
end

"""
    calculer_formule6B_divergence_kl_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 6B : Entropie Relative - Divergence KL (VERSION THÉORIQUE)
Formule : D_KL_theo_unif(P_n||U_n) = ∑ p_theo(x) log₂(p_theo(x)/p_unif(x))

Mesure la divergence entre distribution théorique INDEX5 et uniforme.
"""
function calculer_formule6B_divergence_kl_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Calculer la divergence KL théorique vs uniforme
    divergence = zero(T)
    processed_values = Set{String}()

    for value in subsequence
        if value ∉ processed_values
            push!(processed_values, value)

            p_theo = get(formulas.theoretical_probs, value, zero(T))
            p_unif = T(1.0/18.0)  # Distribution uniforme de référence

            if p_theo > zero(T) && p_unif > zero(T)
                divergence += p_theo * (log(p_theo/p_unif) / log(formulas.base))
            end
        end
    end

    return divergence
end

"""
    calculer_formule7B_information_mutuelle_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 7B : Information Mutuelle Cumulative (VERSION THÉORIQUE)
Formule EXACTE selon cours d'entropie : I(X;Y) = ∑∑ p(x,y) log₂ (p(x,y)/(p(x)p(y)))

CORRECTION SELON COURS D'ENTROPIE :
- Mesure la dépendance théorique entre X_t et X_{t+1} selon le modèle INDEX5
- Utilise les probabilités théoriques INDEX5 pour calculer p(x,y) = p(x) * p(y) (indépendance)
- Pour un modèle indépendant, I(X;Y) = 0 théoriquement
"""
function calculer_formule7B_information_mutuelle_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 2 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # CORRECTION : Calculer l'information mutuelle théorique selon le modèle INDEX5
    # Pour un modèle théorique indépendant (INDEX5), les variables sont indépendantes
    # Donc I(X_t; X_{t+1}) = 0 théoriquement

    # Cependant, on peut calculer la "pseudo-information mutuelle" basée sur les probabilités INDEX5
    # en supposant que les transitions suivent les probabilités marginales

    # Créer les paires (x_t, x_{t+1}) observées dans la séquence
    pairs = Tuple{String, String}[]
    for i in 1:(length(subsequence)-1)
        push!(pairs, (subsequence[i], subsequence[i+1]))
    end

    if isempty(pairs)
        return zero(T)
    end

    # CORRECTION : InfoMutT doit être identique à InfoMutO mais avec probabilités théoriques
    # Compter les occurrences des paires jointes avec probabilités théoriques
    joint_counts = Dict{Tuple{String, String}, Int}()
    for pair in pairs
        joint_counts[pair] = get(joint_counts, pair, 0) + 1
    end

    # Compter les occurrences marginales avec probabilités théoriques
    x_counts = Dict{String, Int}()
    y_counts = Dict{String, Int}()
    for (x, y) in pairs
        x_counts[x] = get(x_counts, x, 0) + 1
        y_counts[y] = get(y_counts, y, 0) + 1
    end

    # Calculer l'information mutuelle théorique : I(X;Y) = ∑∑ p_theo(x,y) log₂ (p_theo(x,y)/(p_theo(x) p_theo(y)))
    mutual_info = zero(T)
    total_pairs = T(length(pairs))

    for ((x, y), joint_count) in joint_counts
        # Utiliser les probabilités théoriques INDEX5 pour les marginales
        p_x_theo = get(formulas.theoretical_probs, x, formulas.epsilon)
        p_y_theo = get(formulas.theoretical_probs, y, formulas.epsilon)

        # Calculer p_theo(x,y) basé sur la fréquence observée mais pondéré par les probabilités théoriques
        # Approche : utiliser la fréquence relative observée mais évaluée avec les probabilités théoriques
        p_xy_theo = T(joint_count) / total_pairs

        if p_xy_theo > zero(T) && p_x_theo > zero(T) && p_y_theo > zero(T)
            # I(X;Y) += p_theo(x,y) * log₂(p_theo(x,y) / (p_theo(x) * p_theo(y)))
            mutual_info += p_xy_theo * (log(p_xy_theo / (p_x_theo * p_y_theo)) / log(formulas.base))
        end
    end

    return mutual_info
end

"""
    calculer_formule8B_entropie_croisee_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 8B : Entropie Croisée Cumulative (VERSION THÉORIQUE)
Formule : H_cross_theo_unif(P_n, U_n) = -∑ p_theo(x) log₂ q_unif(x)

Mesure le coût d'encodage avec distribution uniforme vs théorique INDEX5.
"""
function calculer_formule8B_entropie_croisee_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Calculer l'entropie croisée théorique
    cross_entropy = zero(T)
    processed_values = Set{String}()

    for value in subsequence
        if value ∉ processed_values
            push!(processed_values, value)

            p_theo = get(formulas.theoretical_probs, value, zero(T))
            q_unif = T(1.0/18.0)  # Distribution uniforme de référence

            if p_theo > zero(T)
                if q_unif > zero(T)
                    cross_entropy -= p_theo * (log(q_unif) / log(formulas.base))
                else
                    cross_entropy -= p_theo * (log(formulas.epsilon) / log(formulas.base))
                end
            end
        end
    end

    return cross_entropy
end

"""
    calculer_formule9B_entropie_topologique_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 9B : Entropie Topologique Cumulative (VERSION THÉORIQUE)
Formule : h_top_theo(f) = lim_{n→∞} (1/n) log₂ |{orbites périodiques de période ≤ n}|

Approximation théorique basée sur la complexité du modèle INDEX5.
"""
function calculer_formule9B_entropie_topologique_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # CORRECTION : Utiliser les probabilités théoriques INDEX5
    # Calculer la complexité théorique basée sur les probabilités INDEX5
    unique_values = Set{String}()
    for value in subsequence
        push!(unique_values, value)
    end

    # Estimer l'entropie topologique théorique
    # h_top ≈ (1/n) log₂(nombre de patterns distincts pondérés par probabilités théoriques)
    complexity = zero(T)

    for value in unique_values
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            # Contribution de chaque valeur à la complexité topologique
            # Pondérée par sa probabilité théorique
            complexity += p_theo * (log(T(1)/p_theo) / log(formulas.base))
        end
    end

    # Normaliser par la longueur de la séquence
    return complexity / n
end

"""
    calculer_formule10B_block_cumulative_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 10B : Entropie de Block Cumulative (VERSION THÉORIQUE)
Formule : H_n_theo = H_theo(X₁, X₂, ..., Xₙ)

Identique à l'entropie jointe de Shannon théorique - incluse pour cohérence avec Formules1.txt.
"""
function calculer_formule10B_block_cumulative_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    # Cette formule est identique à la formule 1B (Shannon Jointe théorique)
    return calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)
end

"""
    calculer_formule11B_conditionnelle_decroissante_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 11B : Entropie Conditionnelle Décroissante (VERSION THÉORIQUE)
Formule : u_n_theo = H_theo(Xₙ|X₁, X₂, ..., Xₙ₋₁)

Identique à la formule 5B (Conditionnelle Cumulative théorique) - incluse pour cohérence.
"""
function calculer_formule11B_conditionnelle_decroissante_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    # Cette formule est identique à la formule 5B (Conditionnelle Cumulative théorique)
    return calculer_formule5B_conditionnelle_theo(formulas, sequence, n)
end

"""
    calculer_formule12B_theoreme_aep_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 12B : Théorème AEP Shannon-McMillan-Breiman (VERSION THÉORIQUE)
Formule : lim_{n→∞} -(1/n) log₂ p_theo(X₁ⁿ) = H_theo(Ξ)

Convergence vers le taux d'entropie théorique selon le théorème AEP.
"""
function calculer_formule12B_theoreme_aep_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Calculer -(1/n) log₂ p_theo(X₁ⁿ) selon le théorème AEP
    # Ceci est équivalent à la formule 2B (AEP théorique)
    return calculer_formule2B_aep_theo(formulas, sequence, n)
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS D'INTÉGRATION DES NOUVELLES CLASSES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_toutes_formules_pour_main_n(sequence::Vector{String}, n::Int) -> Dict{String, Float64}

Calcule toutes les 24 formules (12 OBS + 12 THEO) pour la main n.
Retourne un dictionnaire avec les résultats de chaque formule.

# Arguments
- `sequence::Vector{String}`: Séquence INDEX5 complète
- `n::Int`: Numéro de la main (fenêtre croissante de 1 à n)

# Returns
- `Dict{String, Float64}`: Dictionnaire contenant tous les résultats
"""
function calculer_toutes_formules_pour_main_n(sequence::Vector{String}, n::Int)
    # Initialiser les classes de formules
    formulas_obs = FormulasObserved{Float64}()
    formulas_theo = FormulasTheoretical{Float64}()

    # Dictionnaire pour stocker tous les résultats
    resultats = Dict{String, Float64}()

    # ─────────────────────────────────────────────────────────────────────────
    # CALCUL DES 12 FORMULES OBSERVÉES (OBS)
    # ─────────────────────────────────────────────────────────────────────────

    resultats["1A_Shannon_Jointe_OBS"] = calculer_formule1A_shannon_jointe_obs(formulas_obs, sequence, n)
    resultats["2A_AEP_OBS"] = calculer_formule2A_aep_obs(formulas_obs, sequence, n)
    resultats["3A_Taux_Entropie_OBS"] = calculer_formule3A_taux_entropie_obs(formulas_obs, sequence, n)
    resultats["4A_Entropie_Metrique_OBS"] = calculer_formule4A_entropie_metrique_obs(formulas_obs, sequence, n)
    resultats["5A_Conditionnelle_OBS"] = calculer_formule5A_conditionnelle_obs(formulas_obs, sequence, n)
    resultats["6A_Divergence_KL_OBS"] = calculer_formule6A_divergence_kl_obs(formulas_obs, sequence, n)
    resultats["7A_Information_Mutuelle_OBS"] = calculer_formule7A_information_mutuelle_obs(formulas_obs, sequence, n)
    resultats["8A_Entropie_Croisee_OBS"] = calculer_formule8A_entropie_croisee_obs(formulas_obs, sequence, n)
    resultats["9A_Entropie_Topologique_OBS"] = calculer_formule9A_entropie_topologique_obs(formulas_obs, sequence, n)
    resultats["10A_Block_Cumulative_OBS"] = calculer_formule10A_block_cumulative_obs(formulas_obs, sequence, n)
    resultats["11A_Conditionnelle_Decroissante_OBS"] = calculer_formule11A_conditionnelle_decroissante_obs(formulas_obs, sequence, n)
    resultats["12A_Theoreme_AEP_OBS"] = calculer_formule12A_theoreme_aep_obs(formulas_obs, sequence, n)

    # ─────────────────────────────────────────────────────────────────────────
    # CALCUL DES 12 FORMULES THÉORIQUES (THEO)
    # ─────────────────────────────────────────────────────────────────────────

    resultats["1B_Shannon_Jointe_THEO"] = calculer_formule1B_shannon_jointe_theo(formulas_theo, sequence, n)
    resultats["2B_AEP_THEO"] = calculer_formule2B_aep_theo(formulas_theo, sequence, n)
    resultats["3B_Taux_Entropie_THEO"] = calculer_formule3B_taux_entropie_theo(formulas_theo, sequence, n)
    resultats["4B_Entropie_Metrique_THEO"] = calculer_formule4B_entropie_metrique_theo(formulas_theo, sequence, n)
    resultats["5B_Conditionnelle_THEO"] = calculer_formule5B_conditionnelle_theo(formulas_theo, sequence, n)
    resultats["6B_Divergence_KL_THEO"] = calculer_formule6B_divergence_kl_theo(formulas_theo, sequence, n)
    resultats["7B_Information_Mutuelle_THEO"] = calculer_formule7B_information_mutuelle_theo(formulas_theo, sequence, n)
    resultats["8B_Entropie_Croisee_THEO"] = calculer_formule8B_entropie_croisee_theo(formulas_theo, sequence, n)
    resultats["9B_Entropie_Topologique_THEO"] = calculer_formule9B_entropie_topologique_theo(formulas_theo, sequence, n)
    resultats["10B_Block_Cumulative_THEO"] = calculer_formule10B_block_cumulative_theo(formulas_theo, sequence, n)
    resultats["11B_Conditionnelle_Decroissante_THEO"] = calculer_formule11B_conditionnelle_decroissante_theo(formulas_theo, sequence, n)
    resultats["12B_Theoreme_AEP_THEO"] = calculer_formule12B_theoreme_aep_theo(formulas_theo, sequence, n)

    return resultats
end

"""
    afficher_resultats_formules(resultats::Dict{String, Float64}, n::Int)

Affiche de manière formatée tous les résultats des 24 formules pour la main n.
"""
function afficher_resultats_formules(resultats::Dict{String, Float64}, n::Int)
    println("\n" * "="^80)
    println("RÉSULTATS DES 24 FORMULES POUR LA MAIN $n")
    println("="^80)

    println("\n📊 FORMULES OBSERVÉES (OBS) - Basées sur les fréquences réelles:")
    println("-"^60)
    obs_keys = sort([k for k in keys(resultats) if contains(k, "_OBS")])
    for key in obs_keys
        println("  $key: $(round(resultats[key], digits=6))")
    end

    println("\n🎯 FORMULES THÉORIQUES (THEO) - Basées sur le modèle INDEX5:")
    println("-"^60)
    theo_keys = sort([k for k in keys(resultats) if contains(k, "_THEO")])
    for key in theo_keys
        println("  $key: $(round(resultats[key], digits=6))")
    end

    println("\n" * "="^80)
end

"""
    tester_24_formules_interactive(default_filepath::String)

Interface interactive pour tester les 24 formules (12 OBS + 12 THEO) sur une séquence.
"""
function tester_24_formules_interactive(default_filepath::String)
    println("\n🧮 TEST DES 24 FORMULES (OBS + THEO)")
    println("="^50)

    # Charger les données
    print("Fichier JSON (Entrée = défaut '$default_filepath'): ")
    filepath_input = readline()
    filepath = isempty(filepath_input) ? default_filepath : filepath_input

    if !isfile(filepath)
        println("❌ Fichier non trouvé: $filepath")
        return
    end

    try
        # Charger et traiter les données
        println("📂 Chargement des données...")
        data = load_json_data(filepath)

        if isempty(data)
            println("❌ Aucune donnée trouvée dans le fichier")
            return
        end

        # Sélectionner une partie
        println("\n📊 Parties disponibles:")
        for (i, game) in enumerate(data[1:min(5, length(data))])
            sequence = get(game, "index5_sequence", String[])
            println("  $i. Partie avec $(length(sequence)) mains")
        end

        print("\nChoisir une partie (1-$(min(5, length(data)))): ")
        game_choice = readline()
        game_index = tryparse(Int, game_choice)

        if game_index === nothing || game_index < 1 || game_index > min(5, length(data))
            println("❌ Choix invalide")
            return
        end

        # Extraire la séquence
        selected_game = data[game_index]
        sequence = get(selected_game, "index5_sequence", String[])

        if isempty(sequence)
            println("❌ Séquence vide pour cette partie")
            return
        end

        println("\n✅ Séquence chargée: $(length(sequence)) mains")
        println("Premiers éléments: $(sequence[1:min(10, length(sequence))])")

        # Demander pour quelle main calculer
        print("\nPour quelle main calculer les formules (1-$(length(sequence))): ")
        main_input = readline()
        n = tryparse(Int, main_input)

        if n === nothing || n < 1 || n > length(sequence)
            println("❌ Numéro de main invalide")
            return
        end

        # Calculer toutes les formules
        println("\n🔄 Calcul des 24 formules en cours...")
        resultats = calculer_toutes_formules_pour_main_n(sequence, n)

        # Afficher les résultats
        afficher_resultats_formules(resultats, n)

        # Proposer de sauvegarder
        print("\n💾 Sauvegarder les résultats dans un fichier? (o/N): ")
        save_choice = lowercase(strip(readline()))

        if save_choice == "o" || save_choice == "oui"
            filename = "resultats_24_formules_main_$(n)_$(Dates.format(now(), "yyyymmdd_HHMMSS")).txt"

            open(filename, "w") do file
                write(file, "RÉSULTATS DES 24 FORMULES POUR LA MAIN $n\n")
                write(file, "="^80 * "\n\n")
                write(file, "Séquence analysée: $(length(sequence)) mains\n")
                write(file, "Date: $(Dates.format(now(), "yyyy-mm-dd HH:MM:SS"))\n\n")

                write(file, "FORMULES OBSERVÉES (OBS):\n")
                write(file, "-"^40 * "\n")
                obs_keys = sort([k for k in keys(resultats) if contains(k, "_OBS")])
                for key in obs_keys
                    write(file, "$key: $(resultats[key])\n")
                end

                write(file, "\nFORMULES THÉORIQUES (THEO):\n")
                write(file, "-"^40 * "\n")
                theo_keys = sort([k for k in keys(resultats) if contains(k, "_THEO")])
                for key in theo_keys
                    write(file, "$key: $(resultats[key])\n")
                end
            end

            println("✅ Résultats sauvegardés dans: $filename")
        end

    catch e
        println("❌ Erreur lors du traitement: $e")
    end
end

"""
    generer_nouveau_tableau_24_formules(sequence::Vector{String}, max_mains::Int = -1) -> String

Génère le nouveau tableau avec les 24 formules organisées :
- Gauche : 12 formules THEO (théoriques)
- Centre : INDEX5_Observé
- Droite : 12 formules OBS (observées)

# Arguments
- `sequence::Vector{String}`: Séquence INDEX5 complète
- `max_mains::Int`: Nombre maximum de mains à afficher (-1 = toutes les mains)

# Returns
- `String`: Tableau formaté prêt à afficher
"""
function generer_nouveau_tableau_24_formules(sequence::Vector{String}, max_mains::Int = -1)
    if isempty(sequence)
        return "❌ Séquence vide"
    end

    # Déterminer le nombre de mains à traiter
    nb_mains = max_mains == -1 ? length(sequence) : min(max_mains, length(sequence))

    # Buffer pour construire le tableau
    tableau = IOBuffer()

    # ═══════════════════════════════════════════════════════════════════════════════
    # EN-TÊTE DU NOUVEAU TABLEAU
    # ═══════════════════════════════════════════════════════════════════════════════

    println(tableau, "\n📊 NOUVEAU TABLEAU - 24 FORMULES D'ENTROPIE:")
    println(tableau, "="^150)

    # En-tête avec TOUTES les 24 formules sur UNE SEULE LIGNE - PARFAITEMENT ALIGNÉ
    println(tableau, @sprintf("%-4s | %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s | %-15s | %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s",
            "Main",
            # 12 formules THEO (gauche)
            "ShannonT", "AEPT", "TauxT", "MetricT", "CondT", "DivKLT",
            "InfoMutT", "CrossT", "TopoT", "BlockT", "CondDecT", "TheoAEPT",
            # Centre
            "INDEX5_Observé",
            # 12 formules OBS (droite)
            "ShannonO", "AEPO", "TauxO", "MetricO", "CondO", "DivKLO",
            "InfoMutO", "CrossO", "TopoO", "BlockO", "CondDecO", "TheoAEPO"))

    println(tableau, "-"^220)

    # ═══════════════════════════════════════════════════════════════════════════════
    # DONNÉES DU TABLEAU
    # ═══════════════════════════════════════════════════════════════════════════════

    for n in 1:nb_mains
        # Calculer toutes les formules pour la main n
        resultats = calculer_toutes_formules_pour_main_n(sequence, n)

        # INDEX5 observé pour cette main
        index5_obs = sequence[n]

        # UNE SEULE LIGNE PARFAITEMENT ALIGNÉE : Main + 12 THEO + INDEX5 + 12 OBS
        println(tableau, @sprintf("%-4d | %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f | %-15s | %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f",
                n,
                # 12 formules THEO (gauche)
                resultats["1B_Shannon_Jointe_THEO"],
                resultats["2B_AEP_THEO"],
                resultats["3B_Taux_Entropie_THEO"],
                resultats["4B_Entropie_Metrique_THEO"],
                resultats["5B_Conditionnelle_THEO"],
                resultats["6B_Divergence_KL_THEO"],
                resultats["7B_Information_Mutuelle_THEO"],
                resultats["8B_Entropie_Croisee_THEO"],
                resultats["9B_Entropie_Topologique_THEO"],
                resultats["10B_Block_Cumulative_THEO"],
                resultats["11B_Conditionnelle_Decroissante_THEO"],
                resultats["12B_Theoreme_AEP_THEO"],
                # Centre
                index5_obs,
                # 12 formules OBS (droite)
                resultats["1A_Shannon_Jointe_OBS"],
                resultats["2A_AEP_OBS"],
                resultats["3A_Taux_Entropie_OBS"],
                resultats["4A_Entropie_Metrique_OBS"],
                resultats["5A_Conditionnelle_OBS"],
                resultats["6A_Divergence_KL_OBS"],
                resultats["7A_Information_Mutuelle_OBS"],
                resultats["8A_Entropie_Croisee_OBS"],
                resultats["9A_Entropie_Topologique_OBS"],
                resultats["10A_Block_Cumulative_OBS"],
                resultats["11A_Conditionnelle_Decroissante_OBS"],
                resultats["12A_Theoreme_AEP_OBS"]))
    end

    println(tableau, "-"^220)
    println(tableau, "📝 LÉGENDE:")
    println(tableau, "   THEO (Gauche) : 12 formules théoriques basées sur le modèle INDEX5")
    println(tableau, "   OBS (Droite)  : 12 formules observées basées sur les fréquences réelles")
    println(tableau, "   Centre        : INDEX5 réellement observé pour chaque main")
    println(tableau, "   Total mains affichées : $nb_mains / $(length(sequence))")

    return String(take!(tableau))
end

"""
    generer_tableau_differentiels_24_formules(sequence::Vector{String}, max_mains::Int = -1) -> String

Génère le tableau des différentiels avec les 24 formules organisées :
- Gauche : 12 différentiels THEO (théoriques)
- Centre : INDEX5_Observé
- Droite : 12 différentiels OBS (observées)

# Arguments
- `sequence::Vector{String}`: Séquence INDEX5 complète
- `max_mains::Int`: Nombre maximum de mains à afficher (-1 = toutes les mains)

# Returns
- `String`: Tableau des différentiels formaté prêt à afficher
"""
function generer_tableau_differentiels_24_formules(sequence::Vector{String}, max_mains::Int = -1)
    tableau = IOBuffer()

    if length(sequence) < 2
        println(tableau, "❌ Erreur: Séquence trop courte pour calculer les différentiels (minimum 2 mains)")
        return String(take!(tableau))
    end

    # Initialiser les structures de formules
    formulas_obs = FormulasObserved{Float64}(2.0, 1e-10)
    formulas_theo = FormulasTheoretical{Float64}(2.0, 1e-10)

    # Initialiser la structure des différentiels (main 2 à 60)
    start_main = 2
    end_main = min(length(sequence), max_mains > 0 ? max_mains : length(sequence))
    diff_metrics = DifferentialMetrics{Float64}(start_main, end_main, 2.0)

    # Calculer tous les différentiels
    calculer_differentiels!(diff_metrics, formulas_obs, formulas_theo, sequence)

    # ═══════════════════════════════════════════════════════════════════════════════
    # EN-TÊTE DU TABLEAU DES DIFFÉRENTIELS
    # ═══════════════════════════════════════════════════════════════════════════════

    println(tableau, "\n📊 TABLEAU DES DIFFÉRENTIELS - 24 FORMULES D'ENTROPIE:")
    println(tableau, "="^170)

    # En-tête avec TOUS les 24 différentiels - LARGEURS AJUSTÉES SELON LA LONGUEUR DES NOMS
    println(tableau, @sprintf("%-4s | %-9s %-8s %-9s %-9s %-9s %-9s %-9s %-8s %-9s %-9s %-9s %-9s | %-15s | %-9s %-8s %-9s %-9s %-9s %-9s %-9s %-8s %-9s %-9s %-9s %-9s",
            "Main",
            # 12 différentiels THEO (gauche) - LARGEURS AJUSTÉES
            "DiffShanT", "DiffAEPT", "DiffTauxT", "DiffMetrT", "DiffCondT", "DiffDivKT",
            "DiffInfoT", "DiffCroT", "DiffTopoT", "DiffBlocT", "DiffConDT", "DiffThAET",
            # Centre
            "INDEX5_Observé",
            # 12 différentiels OBS (droite) - LARGEURS AJUSTÉES
            "DiffShanO", "DiffAEPO", "DiffTauxO", "DiffMetrO", "DiffCondO", "DiffDivKO",
            "DiffInfoO", "DiffCroO", "DiffTopoO", "DiffBlocO", "DiffConDO", "DiffThAEO"
    ))

    # Ligne de séparation - AJUSTÉE À LA NOUVELLE LARGEUR
    println(tableau, "-"^170)

    # ═══════════════════════════════════════════════════════════════════════════════
    # DONNÉES DU TABLEAU DES DIFFÉRENTIELS
    # ═══════════════════════════════════════════════════════════════════════════════

    for (idx, n) in enumerate(start_main:end_main)
        if n > length(sequence)
            break
        end

        # Valeur INDEX5 observée pour cette main
        index5_value = sequence[n]

        # Ligne de données avec TOUS les 24 différentiels - LARGEURS AJUSTÉES SELON LES NOMS
        println(tableau, @sprintf("%-4d | %-9.4f %-8.4f %-9.4f %-9.4f %-9.4f %-9.4f %-9.4f %-8.4f %-9.4f %-9.4f %-9.4f %-9.4f | %-15s | %-9.4f %-8.4f %-9.4f %-9.4f %-9.4f %-9.4f %-9.4f %-8.4f %-9.4f %-9.4f %-9.4f %-9.4f",
                n,
                # 12 différentiels THEO (gauche) - LARGEURS AJUSTÉES
                diff_metrics.diff_shannon_t[idx], diff_metrics.diff_aep_t[idx], diff_metrics.diff_taux_t[idx],
                diff_metrics.diff_metric_t[idx], diff_metrics.diff_cond_t[idx], diff_metrics.diff_divkl_t[idx],
                diff_metrics.diff_infomut_t[idx], diff_metrics.diff_cross_t[idx], diff_metrics.diff_topo_t[idx],
                diff_metrics.diff_block_t[idx], diff_metrics.diff_conddec_t[idx], diff_metrics.diff_theoaep_t[idx],
                # Centre
                index5_value,
                # 12 différentiels OBS (droite) - LARGEURS AJUSTÉES
                diff_metrics.diff_shannon_o[idx], diff_metrics.diff_aep_o[idx], diff_metrics.diff_taux_o[idx],
                diff_metrics.diff_metric_o[idx], diff_metrics.diff_cond_o[idx], diff_metrics.diff_divkl_o[idx],
                diff_metrics.diff_infomut_o[idx], diff_metrics.diff_cross_o[idx], diff_metrics.diff_topo_o[idx],
                diff_metrics.diff_block_o[idx], diff_metrics.diff_conddec_o[idx], diff_metrics.diff_theoaep_o[idx]
        ))
    end

    # Ligne de séparation finale - AJUSTÉE À LA NOUVELLE LARGEUR
    println(tableau, "-"^170)

    # Légende
    println(tableau, "📝 LÉGENDE:")
    println(tableau, "   DIFF THEO (Gauche) : 12 différentiels des formules théoriques |métrique_T(n) - métrique_T(n-1)|")
    println(tableau, "   DIFF OBS (Droite)  : 12 différentiels des formules observées |métrique_O(n) - métrique_O(n-1)|")
    println(tableau, "   Centre             : INDEX5 réellement observé pour chaque main")
    println(tableau, "   Plage des différentiels : Main $(start_main) à $(end_main)")
    println(tableau, "   Total différentiels affichés : $(end_main - start_main + 1) / $(length(sequence))")

    return String(take!(tableau))
end

#
# Cette section regroupe TOUTES les métriques d'entropie avec leurs méthodes de calcul
# selon les définitions exactes du fichier formules_metriques_entropie_baccarat.txt
#
# ORGANISATION COMPLÈTE DES MÉTRIQUES :
#
# 📊 MÉTRIQUES DE BASE (8 métriques principales) :
# 1. Mt5 (Entropie Mt5 - Kolmogorov-Sinai)
# 2. CONDITIONNELLE (Entropie Conditionnelle)
# 3. T5 (Taux d'Entropie - Entropy Rate)
# 4. ENTROPG (Entropie Générale - AEP avec probabilités théoriques)
# 5. DIVENTROPG (Diversité Entropique - Shannon)
# 6. EGobs (Entropie Générale Observée - AEP avec probabilités observées)
# 7. ConfEG (Conformité Entropique - |EntropG - EGobs|)
# 8. StructEG (Structure Entropique - EntropG/EGobs)
#
# 📈 DIFFÉRENTIELS (7 différentiels dérivés) :
# 9. DIFFCOND/DIFFC (Différentiel Entropie Conditionnelle)
# 10. DIFFT5/DIFFT5 (Différentiel T5)
# 11. DIFFDIVRENTROPG/DIFFDIVEG (Différentiel Diversité Entropique)
# 12. DIFFEG/DIFFEG (Différentiel Entropie Générale)
# 13. DIFFEGOBS/DIFFEGOBS (Différentiel Entropie Générale Observée)
# 14. DIFFSEG/DIFFSEG (Différentiel Structure Entropique)
# 15. DIFFCEG/DIFFCEG (Différentiel Conformité Entropique)
#
# 🎯 SCORE COMPOSITE (1 métrique composite) :
# 16. SCORE = (DiffC + DiffEG) / (DiffT5 + DiffDivEG)
#
# TOTAL : 16 métriques complètes avec toutes leurs méthodes de calcul
# ═══════════════════════════════════════════════════════════════════════════════

# ─────────────────────────────────────────────────────────────────────────────
# 1. Mt5 (Entropie Mt5 - Kolmogorov-Sinai)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_metric_from_t5(analyzer::EntropyAnalyzer{T}, t5_value::T) where T -> T

Calcule la Mt5 à partir d'une valeur T5 déjà calculée : Mt5 = T5 / log₂(18)

Formule exacte : Mt5 = T5 / log₂(18)
Où log₂(18) est l'entropie maximale uniforme pour 18 valeurs INDEX5.

Cette fonction évite le recalcul de T5 en utilisant la valeur déjà disponible.
"""
function calculate_metric_from_t5(analyzer::EntropyAnalyzer{T}, t5_value::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # Mt5 = T5 / log₂(18) - Calcul exact
    # log2_18 = log(T(18)) / log(analyzer.base)  # log₂(18) calculé exactement
    # if log2_18 ≈ zero(T)
    #     return zero(T)
    # end
    # return t5_value / log2_18

    # Valeur par défaut temporaire
    return zero(T)
end

"""
    calculate_metric_entropy_new(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

ANCIENNE FONCTION - Conservée pour compatibilité temporaire
Calcule la Mt5 simplifiée : Mt5 = T5 / 4.1699
"""
function calculate_metric_entropy_new(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # Calculer T5 avec probabilités théoriques
    # t5 = calculate_entropy_rate_new(analyzer, sequence)
    # Mt5 = T5 / log₂(18)
    # return calculate_metric_from_t5(analyzer, t5)

    # Valeur par défaut temporaire
    return zero(T)
end

"""
    estimate_metric_entropy(block_entropies::Vector{T}) where T -> T

ANCIENNE MÉTHODE - Conservée pour compatibilité temporaire
Estime l'entropie Mt5 (Kolmogorov-Sinai) : h_μ(T) ≈ H(max_length)/max_length

Formule théorique : h_μ(T) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
Implémentation pratique : h_metric = H(max_length)/max_length
"""
function estimate_metric_entropy(block_entropies::Vector{T}) where T<:AbstractFloat
    if isempty(block_entropies)
        return zero(T)
    end

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # Approximation de la limite : h_metric = H(max_length)/max_length
    # max_length = length(block_entropies)
    # return block_entropies[end] / max_length

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 2. CONDITIONNELLE (Entropie Conditionnelle)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_conditional_entropy(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Calcule l'entropie conditionnelle H(Xₙ|Xₙ₋₁) avec contexte de longueur 1.

Formule théorique : H(X|Y) = ∑ P(y) × H(X|Y=y) = -∑∑ P(x,y) × log₂ P(x|y)
Implémentation pratique : Contexte de longueur 1 : H(Xₙ|Xₙ₋₁)
"""
function calculate_conditional_entropy(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if length(sequence) < 2
        return zero(T)
    end

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # Compter les transitions contexte → symbole suivant
    # context_transitions = Dict{String, Dict{String, Int}}()
    # for i in 1:(length(sequence)-1)
    #     context = sequence[i]
    #     next_symbol = sequence[i+1]
    #     if !haskey(context_transitions, context)
    #         context_transitions[context] = Dict{String, Int}()
    #     end
    #     context_transitions[context][next_symbol] =
    #         get(context_transitions[context], next_symbol, 0) + 1
    # end
    # # Calculer H(X|Contexte) = ∑ P(contexte) × H(X|contexte)
    # total_transitions = sum(sum(values(transitions)) for transitions in values(context_transitions))
    # conditional_entropy = zero(T)
    # for (context, transitions) in context_transitions
    #     context_prob = T(sum(values(transitions))) / T(total_transitions)
    #     # Créer séquence des symboles suivants pour ce contexte
    #     context_sequence = String[]
    #     for (next_symbol, count) in transitions
    #         append!(context_sequence, fill(next_symbol, count))
    #     end
    #     # H(X|ce contexte) calculé selon AEP
    #     context_entropy = calculate_sequence_entropy_aep(analyzer, context_sequence)
    #     conditional_entropy += context_prob * context_entropy
    # end
    # return conditional_entropy

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 3. T5 (Taux d'Entropie - Entropy Rate)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_entropy_rate_new(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Calcule le T5 selon la nouvelle méthode théoriquement correcte.

Nouvelle formule : T5 = H_AEP(fenêtre_courante_de_5_éléments)
Où fenêtre_courante = [Xₙ₋₄, Xₙ₋₃, Xₙ₋₂, Xₙ₋₁, Xₙ]

Cette méthode calcule l'entropie jointe H₅ de la fenêtre de 5 éléments
au lieu de faire la moyenne des fenêtres glissantes.
"""
function calculate_entropy_rate_new(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T<:AbstractFloat
    # Vérifier qu'on a au moins 5 éléments
    if length(sequence) < 5
        return zero(T)
    end

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # Extraire la fenêtre de 5 derniers éléments
    # window_5 = sequence[(end-4):end]
    # Calculer H₅ = H_AEP de la fenêtre de 5
    # h5 = calculate_sequence_entropy_aep(analyzer, window_5)
    # T5 = H₅ directement (pas de division supplémentaire)
    # return h5

    # Valeur par défaut temporaire
    return zero(T)
end

"""
    calculate_entropy_rate(block_entropies::Vector{T}) where T -> T

ANCIENNE MÉTHODE - Conservée pour compatibilité temporaire
Calcule le T5 : entropy_rate = block_entropies[-1]

Formule théorique : H_rate = lim_{n→∞} (1/n) H(X₁, X₂, ..., Xₙ)
Implémentation pratique : T5 = block_entropies[-1] (entropie du dernier bloc)
"""
function calculate_entropy_rate(block_entropies::Vector{T}) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return isempty(block_entropies) ? zero(T) : block_entropies[end]

    # Valeur par défaut temporaire
    return zero(T)
end

"""
    calculate_block_entropies(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}, max_length::Int) where T -> Vector{T}

Calcule les entropies de blocs de longueurs croissantes pour le calcul du T5.
Utilisé par les métriques Mt5 et T5 (ancienne méthode - conservée pour compatibilité).
"""
function calculate_block_entropies(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    max_length::Int
) where T<:AbstractFloat
    entropies = T[]

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # for block_len in 1:min(max_length, length(sequence))
    #     if block_len == 1
    #         # Pour longueur 1 : entropie AEP de la séquence complète
    #         block_entropy = calculate_sequence_entropy_aep(analyzer, sequence)
    #     else
    #         # Pour longueur > 1 : entropie moyenne des sous-séquences
    #         total_entropy = zero(T)
    #         num_blocks = 0
    #         for i in 1:(length(sequence) - block_len + 1)
    #             block_sequence = sequence[i:(i + block_len - 1)]
    #             total_entropy += calculate_sequence_entropy_aep(analyzer, block_sequence)
    #             num_blocks += 1
    #         end
    #         block_entropy = num_blocks > 0 ? total_entropy / num_blocks : zero(T)
    #     end
    #     push!(entropies, block_entropy)
    # end

    # Valeur par défaut temporaire
    for i in 1:min(max_length, length(sequence))
        push!(entropies, zero(T))
    end

    return entropies
end

# ─────────────────────────────────────────────────────────────────────────────
# 4. ENTROPG (Entropie Générale - AEP)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_sequence_entropy_aep(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Calcule l'entropie d'une séquence selon la formule AEP (Asymptotic Equipartition Property).

Formule théorique AEP : H_AEP = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))
Pour séquences indépendantes : p(X₁, X₂, ..., Xₙ) = ∏ᵢ₌₁ⁿ p_théo(xᵢ)
"""
function calculate_sequence_entropy_aep(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # total_log_prob = zero(T)
    # for value in sequence
    #     p_theo = get(analyzer.theoretical_probs, value, zero(T))
    #     total_log_prob += safe_log(p_theo, analyzer.base, analyzer.epsilon)
    # end
    # return -total_log_prob / length(sequence)

    # Valeur par défaut temporaire
    return zero(T)
end

"""
    calculate_sequence_entropy_aep_observed(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Calcule l'entropie d'une séquence selon la formule AEP avec probabilités observées (EGobs).

Formule AEP observé : H_AEP_obs = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_obs(xᵢ))
où p_obs(xᵢ) = fréquence observée de xᵢ dans la séquence / longueur totale

Cette métrique mesure l'information moyenne par main selon les patterns réellement observés,
sans référence au modèle théorique INDEX5.
"""
function calculate_sequence_entropy_aep_observed(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # 1. Compter les occurrences de chaque valeur
    # counts = Dict{String, Int}()
    # for value in sequence
    #     counts[value] = get(counts, value, 0) + 1
    # end
    # 2. Calculer la somme des logarithmes avec probabilités observées
    # n = length(sequence)
    # total_log_prob = zero(T)
    # for value in sequence
    #     p_obs = T(counts[value]) / T(n)  # Probabilité observée
    #     total_log_prob += safe_log(p_obs, analyzer.base, analyzer.epsilon)
    # end
    # 3. Appliquer la formule AEP observé
    # return -total_log_prob / n

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 5. DIVENTROPG (Diversité Entropique - Shannon)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_shannon_entropy(analyzer::EntropyAnalyzer{T}, probabilities::Vector{T}) where T -> T

Calcule l'entropie de Shannon (DivEntropG) : H(X) = -∑ p_obs(x) log₂ p_obs(x)

Formule théorique : H(X) = -∑ p_obs(x) log₂ p_obs(x)
où p_obs(x) = fréquence observée de x / longueur totale
"""
function calculate_shannon_entropy(
    analyzer::EntropyAnalyzer{T},
    probabilities::Vector{T}
) where T<:AbstractFloat
    probs = validate_probabilities(probabilities)

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # entropy = zero(T)
    # for p in probs
    #     if p > zero(T)
    #         entropy -= p * safe_log(p, analyzer.base, analyzer.epsilon)
    #     end
    # end
    # return entropy

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 6. EGobs (Entropie Générale Observée - AEP avec probabilités observées)
# ─────────────────────────────────────────────────────────────────────────────

# La fonction calculate_sequence_entropy_aep_observed est définie plus haut dans la section 4. ENTROPG

# ─────────────────────────────────────────────────────────────────────────────
# 7. ConfEG et StructEG (Métriques de Conformité et Structure)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_conf_eg(entrop_g::T, eg_obs::T) where T -> T

Calcule ConfEG = |EntropG - EGobs| (Conformité Entropique).

Formule : ConfEG = |H_AEP_théo - H_AEP_obs|
Cette métrique mesure l'écart absolu entre l'information théorique attendue
selon le modèle INDEX5 et l'information réelle selon les patterns observés.

Interprétation :
- ≈ 0.0 : Parfaite conformité au modèle
- 0.1-0.5 : Légère déviation
- 0.5-1.0 : Déviation modérée
- > 1.0 : Forte déviation, anomalie détectée
"""
function calculate_conf_eg(entrop_g::T, eg_obs::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(entrop_g - eg_obs)

    # Valeur par défaut temporaire
    return zero(T)
end

"""
    calculate_struct_eg(entrop_g::T, eg_obs::T) where T -> T

Calcule StructEG = EntropG/EGobs (Structure Entropique).

Formule : StructEG = H_AEP_théo / H_AEP_obs
Cette métrique mesure le facteur multiplicatif entre l'information théorique
et l'information observée.

Interprétation :
- ≈ 1.0 : Équilibre parfait (Réalité = Théorie)
- > 1.0 : Surestimation théorique (séquence plus prévisible que prévu)
- < 1.0 : Sous-estimation théorique (séquence plus complexe que prévu)
- >> 2.0 : Patterns très répétitifs détectés
- << 0.5 : Chaos apparent, complexité extrême
"""
function calculate_struct_eg(entrop_g::T, eg_obs::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # if eg_obs ≈ zero(T)
    #     return T(Inf)  # Division par zéro -> infini
    # end
    # return entrop_g / eg_obs

    # Valeur par défaut temporaire
    return one(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 9. DIFFCOND/DIFFC (Différentiel Entropie Conditionnelle)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_diff_cond(current_conditional::T, previous_conditional::T) where T -> T

Calcule DiffC = DiffCond (Différentiel Entropie Conditionnelle).

Formule : DiffCond(n) = |Conditionnelle(n) - Conditionnelle(n-1)|
DiffC = DiffCond (abréviation dans les tableaux)
"""
function calculate_diff_cond(current_conditional::T, previous_conditional::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(current_conditional - previous_conditional)

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 10. DIFFT5/DIFFT5 (Différentiel T5)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_diff_taux(current_rate::T, previous_rate::T) where T -> T

Calcule DiffT5 = DiffT5 (Différentiel T5).

Formule : DiffT5(n) = |T5(n) - T5(n-1)|
DiffT5 = DiffT5 (abréviation dans les tableaux)
"""
function calculate_diff_taux(current_rate::T, previous_rate::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(current_rate - previous_rate)

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 11. DIFFDIVRENTROPG/DIFFDIVEG (Différentiel Diversité Entropique)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_diff_div_entrop_g(current_simple::T, previous_simple::T) where T -> T

Calcule DiffDivEG = DiffDivEntropG (Différentiel Diversité Entropique).

Formule : DiffDivEntropG(n) = |DivEntropG(n) - DivEntropG(n-1)|
                            = |simple_entropy(n) - simple_entropy(n-1)|
DiffDivEG = DiffDivEntropG (abréviation dans les tableaux)
"""
function calculate_diff_div_entrop_g(current_simple::T, previous_simple::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(current_simple - previous_simple)

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 12. DIFFEG/DIFFEG (Différentiel Entropie Générale)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_diff_entrop_g(current_theoretical::T, previous_theoretical::T) where T -> T

Calcule DiffEG = DiffEG (Différentiel Entropie Générale).

Formule : DiffEG(n) = |EntropG(n) - EntropG(n-1)|
                         = |simple_entropy_theoretical(n) - simple_entropy_theoretical(n-1)|
DiffEG = DiffEG (abréviation dans les tableaux)
"""
function calculate_diff_entrop_g(current_theoretical::T, previous_theoretical::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(current_theoretical - previous_theoretical)

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 13. DIFFEGOBS/DIFFEGOBS (Différentiel Entropie Générale Observée)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_diff_egobs(current_egobs::T, previous_egobs::T) where T -> T

Calcule DiffEGobs = DiffEGobs (Différentiel Entropie Générale Observée).

Formule : DiffEGobs(n) = |EGobs(n) - EGobs(n-1)|
où EGobs = entropy_aep_observed (Entropie AEP avec probabilités observées)
"""
function calculate_diff_egobs(current_egobs::T, previous_egobs::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(current_egobs - previous_egobs)

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 14. DIFFSEG/DIFFSEG (Différentiel Structure Entropique)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_diff_seg(current_struct_eg::T, previous_struct_eg::T) where T -> T

Calcule DiffSEG = DiffSEG (Différentiel Structure Entropique).

Formule : DiffSEG(n) = |StructEG(n) - StructEG(n-1)|
où StructEG = EntropG/EGobs (Structure Entropique)
"""
function calculate_diff_seg(current_struct_eg::T, previous_struct_eg::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(current_struct_eg - previous_struct_eg)

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 15. DIFFCEG/DIFFCEG (Différentiel Conformité Entropique)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_diff_ceg(current_conf_eg::T, previous_conf_eg::T) where T -> T

Calcule DiffCEG = DiffCEG (Différentiel Conformité Entropique).

Formule : DiffCEG(n) = |ConfEG(n) - ConfEG(n-1)|
où ConfEG = |EntropG - EGobs| (Conformité Entropique)
"""
function calculate_diff_ceg(current_conf_eg::T, previous_conf_eg::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(current_conf_eg - previous_conf_eg)

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 16. SCORE COMPOSITE
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_predictive_score(mt5::T, diff_eg::T, diff_div_eg::T, diff_c::T, diff_seg::T, diff_ceg::T, diff_t5::T) where T -> T

Calcule le score prédictif composite selon la FORMULE ENTROPIQUE OPTIMALE.

FORMULE ENTROPIQUE OPTIMALE :
SCORE = Mt5 × exp(-DiffEG) × exp(-DiffDivEG) × exp(-DiffC) / ((1 + DiffSEG + DiffCEG) × (1 + DiffT5))

Cette formule combine :
- Mt5 : Complexité informationnelle normalisée (facteur multiplicatif)
- exp(-DiffEG) : Régularisation de la variation information théorique
- exp(-DiffDivEG) : Régularisation de la variation diversité observée
- exp(-DiffC) : Régularisation de la variation prédictibilité contextuelle
- (1 + DiffSEG + DiffCEG) : Stabilisation cohérence théorie/réalité
- (1 + DiffT5) : Contrôle des fluctuations temporelles

Parameters:
    mt5: Mt5 (Entropie Métrique normalisée)
    diff_eg: DiffEG (Variation information théorique)
    diff_div_eg: DiffDivEG (Variation diversité observée)
    diff_c: DiffC (Variation prédictibilité contextuelle)
    diff_seg: DiffSEG (Variation ratio théorie/réalité)
    diff_ceg: DiffCEG (Variation distance théorie/réalité)
    diff_t5: DiffT5 (Variation taux entropique)

Returns:
    SCORE: Score prédictif selon la formule entropique optimale
"""
function calculate_predictive_score(mt5::T, diff_eg::T, diff_div_eg::T, diff_c::T, diff_seg::T, diff_ceg::T, diff_t5::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # Calcul des termes exponentiels de régularisation
    # exp_diff_eg = exp(-diff_eg)
    # exp_diff_div_eg = exp(-diff_div_eg)
    # exp_diff_c = exp(-diff_c)
    # Calcul du dénominateur : (1 + DiffSEG + DiffCEG) × (1 + DiffT5)
    # denominator = (one(T) + diff_seg + diff_ceg) * (one(T) + diff_t5)
    # Gestion du cas dénominateur ≈ 0 (très improbable avec cette formule)
    # if denominator ≈ zero(T)
    #     return T(Inf)  # Score infini
    # else
    #     # Calcul du score selon la formule entropique optimale
    #     numerator = mt5 * exp_diff_eg * exp_diff_div_eg * exp_diff_c
    #     return numerator / denominator
    # end

    # Valeur par défaut temporaire
    return zero(T)
end

# ═══════════════════════════════════════════════════════════════════════════════
# ANALYSE ENTROPIQUE AVANCÉE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculate_block_entropies(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}, max_length::Int) where T -> Vector{T}

Calcule les entropies de blocs de longueurs croissantes.
"""
function calculate_block_entropies(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    max_length::Int
) where T<:AbstractFloat
    entropies = T[]

    for block_len in 1:min(max_length, length(sequence))
        if block_len == 1
            # Pour longueur 1 : entropie AEP de la séquence complète
            block_entropy = calculate_sequence_entropy_aep(analyzer, sequence)
        else
            # Pour longueur > 1 : entropie moyenne des sous-séquences
            total_entropy = zero(T)
            num_blocks = 0

            for i in 1:(length(sequence) - block_len + 1)
                block_sequence = sequence[i:(i + block_len - 1)]
                total_entropy += calculate_sequence_entropy_aep(analyzer, block_sequence)
                num_blocks += 1
            end

            block_entropy = num_blocks > 0 ? total_entropy / num_blocks : zero(T)
        end

        push!(entropies, block_entropy)
    end

    return entropies
end

"""
    calculate_all_metrics_evolution(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}, max_block_length::Int = 5) where T -> Vector{EntropyMetrics{T}}

Calcule l'évolution de TOUTES les métriques d'entropie pour chaque position dans la séquence.
Cette fonction centrale utilise toutes les métriques définies dans cette section.

Métriques calculées pour chaque position :
- Mt5 : Entropie Mt5 (nouvelle méthode avec entropie jointe de fenêtre de 5)
- CONDITIONNELLE : Entropie conditionnelle H(Xₙ|Xₙ₋₁)
- T5 : Taux d'entropie (nouvelle méthode avec entropie jointe de fenêtre de 5)
- ENTROPG : Entropie générale (AEP avec probabilités théoriques)
- DIVENTROPG : Diversité entropique (Shannon avec fréquences observées)
- EGobs : Entropie générale observée (AEP avec probabilités observées)
- ConfEG : Conformité entropique (|EntropG - EGobs|)
- StructEG : Structure entropique (EntropG/EGobs)
"""
function calculate_all_metrics_evolution(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    max_block_length::Int = 5
) where T<:AbstractFloat
    if isempty(sequence)
        return EntropyMetrics{T}[]
    end

    results = EntropyMetrics{T}[]

    for n in 1:length(sequence)
        # Sous-séquence jusqu'à la position n
        subsequence = sequence[1:n]

        # ═══ CALCUL DE TOUTES LES MÉTRIQUES DE BASE ═══

        # 5. DIVENTROPG : Diversité entropique (Shannon avec fréquences observées)
        counts = Dict{String, Int}()
        for value in subsequence
            counts[value] = get(counts, value, 0) + 1
        end
        empirical_probs = T[counts[value] / n for value in keys(counts)]
        simple_entropy_observed = calculate_shannon_entropy(analyzer, empirical_probs)

        # 4. ENTROPG : Entropie générale (AEP avec probabilités théoriques)
        simple_entropy_theoretical = calculate_sequence_entropy_aep(analyzer, subsequence)

        # 2. CONDITIONNELLE : Entropie conditionnelle H(Xₙ|Xₙ₋₁)
        conditional_entropy = calculate_conditional_entropy(analyzer, subsequence)

        # Calcul des entropies de blocs (plus nécessaire - Mt5 et T5 utilisent maintenant leurs propres méthodes)
        block_entropies = calculate_block_entropies(analyzer, subsequence, max_block_length)

        # 3. T5 : Taux d'entropie (entropy rate) - NOUVELLE MÉTHODE
        entropy_rate = calculate_entropy_rate_new(analyzer, subsequence)

        # 1. Mt5 : Entropie Mt5 (Kolmogorov-Sinai) - CALCULÉE À PARTIR DE T5
        metric_entropy = calculate_metric_from_t5(analyzer, entropy_rate)

        # 6. EGobs : Entropie générale observée (AEP avec probabilités observées)
        entropy_aep_observed = calculate_sequence_entropy_aep_observed(analyzer, subsequence)

        # 7. ConfEG : Conformité entropique |EntropG - EGobs|
        conf_eg = calculate_conf_eg(simple_entropy_theoretical, entropy_aep_observed)

        # 8. StructEG : Structure entropique EntropG/EGobs
        struct_eg = calculate_struct_eg(simple_entropy_theoretical, entropy_aep_observed)

        # Créer la structure de métriques complète
        metrics = EntropyMetrics{T}(
            n,                              # position
            n,                              # sequence_length
            length(counts),                 # unique_values
            simple_entropy_observed,        # simple_entropy (DIVENTROPG)
            simple_entropy_theoretical,     # simple_entropy_theoretical (ENTROPG)
            conditional_entropy,            # conditional_entropy (CONDITIONNELLE)
            metric_entropy,                 # metric_entropy (Mt5)
            entropy_rate,                   # entropy_rate (T5)
            entropy_aep_observed,           # entropy_aep_observed (EGobs)
            conf_eg,                        # conf_eg (ConfEG)
            struct_eg,                      # struct_eg (StructEG)
            block_entropies                 # block_entropies (pour calculs avancés)
        )

        push!(results, metrics)
    end

    return results
end

# ═══════════════════════════════════════════════════════════════════════════════
# FIN DE LA SECTION DÉDIÉE - TOUTES LES MÉTRIQUES D'ENTROPIE BACCARAT
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section contient maintenant TOUTES les métriques d'entropie :
# ✅ 8 MÉTRIQUES DE BASE : Mt5, Conditionnelle, T5, EntropG, DivEntropG, EGobs, ConfEG, StructEG
# ✅ 7 DIFFÉRENTIELS : DiffCond/DiffC, DiffT5/DiffT5, DiffDivEntropG/DiffDivEG, DiffEG/DiffEG, DiffEGobs, DiffSEG, DiffCEG
# ✅ 1 SCORE COMPOSITE : SCORE = (DiffC + DiffEG) / (DiffT5 + DiffDivEG)
# ✅ FONCTIONS UTILITAIRES : Calcul des blocs, évolution complète des métriques
#
# TOTAL : 16 métriques + fonctions de support = Section complète et autonome
# ═══════════════════════════════════════════════════════════════════════════════

# ═══════════════════════════════════════════════════════════════════════════════
# RÈGLES INDEX1 DÉTERMINISTES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculate_required_index1(current_index5::String) -> Union{Int, Nothing}

Calcule INDEX1 obligatoire selon les règles déterministes:
- Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
- Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)
"""
function calculate_required_index1(current_index5::String)
    if isempty(current_index5)
        return nothing
    end
    
    try
        parts = split(current_index5, '_')
        if length(parts) < 2
            return nothing
        end
        
        current_index1 = parse(Int, parts[1])
        current_index2 = parts[2]
        
        if current_index2 == "C"
            return 1 - current_index1  # Inversion obligatoire
        else  # A ou B
            return current_index1      # Conservation obligatoire
        end
    catch
        return nothing
    end
end

"""
    get_valid_index5_values(required_index1::Int) -> Vector{String}

Retourne les 9 valeurs INDEX5 possibles avec INDEX1 obligatoire.
"""
function get_valid_index5_values(required_index1::Int)
    valid_values = String[]
    for index2 in ["A", "B", "C"]
        for index3 in ["BANKER", "PLAYER", "TIE"]
            push!(valid_values, "$(required_index1)_$(index2)_$(index3)")
        end
    end
    return valid_values
end

# ═══════════════════════════════════════════════════════════════════════════════
# CHARGEMENT ET EXTRACTION DES DONNÉES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    load_baccarat_data(filepath::String) -> Vector{Dict}

Charge les données de baccarat depuis un fichier JSON.
Gère différentes structures JSON possibles.
"""
function load_baccarat_data(filepath::String)
    try
        data = JSON.parsefile(filepath)
        
        # Vérifier la structure du JSON
        if isa(data, Dict) && haskey(data, "parties_condensees")
            parties = data["parties_condensees"]
            @info "✅ Données chargées: $(length(parties)) parties trouvées"
            return parties
        elseif isa(data, Vector)
            @info "✅ Données chargées: $(length(data)) parties trouvées"
            return data
        else
            @warn "❌ Structure JSON non reconnue"
            return Dict[]
        end
    catch e
        if isa(e, SystemError)
            @error "❌ Erreur: Fichier $filepath non trouvé"
        else
            @error "❌ Erreur JSON: $e"
        end
        return Dict[]
    end
end

"""
    extract_index5_sequence(game_data::Dict) -> Vector{String}

Extrait la séquence INDEX5 d'une partie.
Filtre les mains d'ajustement et extrait uniquement les valeurs INDEX5 valides.
"""
function extract_index5_sequence(game_data::Dict)
    sequence = String[]
    
    # Vérifier différentes structures possibles
    if haskey(game_data, "hands")
        # Structure: {"hands": [...]}
        for hand in game_data["hands"]
            # Exclure les mains d'ajustement
            if (haskey(hand, "main_number") && 
                !isnothing(hand["main_number"]) &&
                haskey(hand, "INDEX5") && 
                !isnothing(hand["INDEX5"]) &&
                !isempty(strip(string(hand["INDEX5"]))))
                push!(sequence, string(hand["INDEX5"]))
            end
        end
    elseif haskey(game_data, "mains_condensees")
        # Structure: {"mains_condensees": [...]}
        for main in game_data["mains_condensees"]
            # Exclure les mains d'ajustement
            if (haskey(main, "main_number") && 
                !isnothing(main["main_number"]) &&
                haskey(main, "index5") && 
                !isnothing(main["index5"]) &&
                !isempty(strip(string(main["index5"]))))
                push!(sequence, string(main["index5"]))
            end
        end
    else
        @warn "❌ Structure de partie non reconnue. Clés disponibles: $(keys(game_data))"
        return String[]
    end
    
    @info "🔍 Séquence extraite: $(length(sequence)) mains valides"
    return sequence
end

# ═══════════════════════════════════════════════════════════════════════════════
# ANALYSE ENTROPIQUE AVANCÉE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculate_block_entropies(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}, max_length::Int) where T -> Vector{T}

Calcule les entropies de blocs de longueurs croissantes.
"""
function calculate_block_entropies(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    max_length::Int
) where T<:AbstractFloat
    entropies = T[]

    for block_len in 1:min(max_length, length(sequence))
        if block_len == 1
            # Pour longueur 1 : entropie AEP de la séquence complète
            block_entropy = calculate_sequence_entropy_aep(analyzer, sequence)
        else
            # Pour longueur > 1 : entropie moyenne des sous-séquences
            total_entropy = zero(T)
            num_blocks = 0

            for i in 1:(length(sequence) - block_len + 1)
                block_sequence = sequence[i:(i + block_len - 1)]
                total_entropy += calculate_sequence_entropy_aep(analyzer, block_sequence)
                num_blocks += 1
            end

            block_entropy = num_blocks > 0 ? total_entropy / num_blocks : zero(T)
        end

        push!(entropies, block_entropy)
    end

    return entropies
end



"""
    calculate_block_entropy_evolution(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}, max_block_length::Int = 5) where T -> Vector{EntropyMetrics{T}}

Calcule l'évolution de l'entropie par blocs pour chaque position dans la séquence.
"""
function calculate_block_entropy_evolution(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    max_block_length::Int = 5
) where T<:AbstractFloat
    if isempty(sequence)
        return EntropyMetrics{T}[]
    end

    results = EntropyMetrics{T}[]

    for n in 1:length(sequence)
        # Sous-séquence jusqu'à la position n
        subsequence = sequence[1:n]

        # Compter les occurrences pour l'entropie empirique
        counts = Dict{String, Int}()
        for value in subsequence
            counts[value] = get(counts, value, 0) + 1
        end

        # Probabilités empiriques
        empirical_probs = T[counts[value] / n for value in keys(counts)]

        # Entropie de Shannon sur fréquences observées
        simple_entropy_observed = calculate_shannon_entropy(analyzer, empirical_probs)

        # Entropie AEP avec probabilités théoriques
        simple_entropy_theoretical = calculate_sequence_entropy_aep(analyzer, subsequence)

        # Entropie conditionnelle
        conditional_entropy = calculate_conditional_entropy(analyzer, subsequence)

        # Entropies de blocs
        block_entropies = calculate_block_entropies(analyzer, subsequence, max_block_length)

        # T5 (nouvelle méthode - entropie jointe de la fenêtre de 5)
        entropy_rate = calculate_entropy_rate_new(analyzer, sequence)

        # Entropie Mt5 (calculée à partir de T5 pour éviter le double calcul)
        metric_entropy = calculate_metric_from_t5(analyzer, entropy_rate)

        # EGobs (entropie générale observée avec probabilités observées)
        entropy_aep_observed = calculate_sequence_entropy_aep_observed(analyzer, sequence)

        # ConfEG et StructEG (métriques de conformité et structure)
        conf_eg = calculate_conf_eg(simple_entropy_theoretical, entropy_aep_observed)
        struct_eg = calculate_struct_eg(simple_entropy_theoretical, entropy_aep_observed)

        # Créer la structure de métriques
        metrics = EntropyMetrics{T}(
            n,                              # position
            n,                              # sequence_length
            length(counts),                 # unique_values
            simple_entropy_observed,        # simple_entropy
            simple_entropy_theoretical,     # simple_entropy_theoretical
            conditional_entropy,            # conditional_entropy
            metric_entropy,                 # metric_entropy
            entropy_rate,                   # entropy_rate
            entropy_aep_observed,           # entropy_aep_observed (EGobs)
            conf_eg,                        # conf_eg (ConfEG)
            struct_eg,                      # struct_eg (StructEG)
            block_entropies                 # block_entropies
        )

        push!(results, metrics)
    end

    return results
end

# ═══════════════════════════════════════════════════════════════════════════════
# CALCULS DIFFÉRENTIELS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculate_differentials(entropy_evolution::Vector{EntropyMetrics{T}}) where T -> Vector{PredictiveDifferentials{T}}

Calcule les différentiels entre mains consécutives.
"""
function calculate_differentials(
    entropy_evolution::Vector{EntropyMetrics{T}}
) where T<:AbstractFloat
    if length(entropy_evolution) < 2
        return PredictiveDifferentials{T}[]
    end

    differentials = PredictiveDifferentials{T}[]

    # Première main : différentiel = 0 (pas de main précédente)
    first_diff = PredictiveDifferentials{T}(
        zero(T), zero(T), zero(T), zero(T), zero(T), zero(T), zero(T), zero(T)  # DiffC, DiffT5, DiffDivEG, DiffEG, DiffEGobs, DiffSEG, DiffCEG, SCORE
    )
    push!(differentials, first_diff)

    # Calculer les différentiels pour les mains suivantes
    for i in 2:length(entropy_evolution)
        current = entropy_evolution[i]
        previous = entropy_evolution[i-1]

        # Calcul des différentiels selon les définitions exactes en utilisant les fonctions dédiées
        diff_cond = calculate_diff_cond(current.conditional_entropy, previous.conditional_entropy)  # DiffC = DiffCond
        diff_taux = calculate_diff_taux(current.entropy_rate, previous.entropy_rate)               # DiffT5 = DiffT5
        diff_div_entrop_g = calculate_diff_div_entrop_g(current.simple_entropy, previous.simple_entropy)   # DiffDivEG = DiffDivEntropG
        diff_entrop_g = calculate_diff_entrop_g(current.simple_entropy_theoretical, previous.simple_entropy_theoretical)  # DiffEG = DiffEG

        # Calcul des nouveaux différentiels
        diff_egobs = calculate_diff_egobs(current.entropy_aep_observed, previous.entropy_aep_observed)  # DiffEGobs
        diff_seg = calculate_diff_seg(current.struct_eg, previous.struct_eg)                           # DiffSEG
        diff_ceg = calculate_diff_ceg(current.conf_eg, previous.conf_eg)                               # DiffCEG

        # Calcul du score composite selon la formule entropique optimale
        # SCORE = Mt5 × exp(-DiffEG) × exp(-DiffDivEG) × exp(-DiffC) / ((1 + DiffSEG + DiffCEG) × (1 + DiffT5))
        score = calculate_predictive_score(
            current.metric_entropy,     # Mt5
            diff_entrop_g,              # DiffEG
            diff_div_entrop_g,          # DiffDivEG
            diff_cond,                  # DiffC
            diff_seg,                   # DiffSEG
            diff_ceg,                   # DiffCEG
            diff_taux                   # DiffT5
        )

        diff = PredictiveDifferentials{T}(
            diff_cond, diff_taux, diff_div_entrop_g, diff_entrop_g, diff_egobs, diff_seg, diff_ceg, score
        )
        push!(differentials, diff)
    end

    return differentials
end

# ═══════════════════════════════════════════════════════════════════════════════
# ANALYSE COMPLÈTE D'UNE PARTIE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    analyze_single_game(analyzer::EntropyAnalyzer{T}, game_data::Dict, game_id::Union{String, Nothing} = nothing) where T -> Dict

Analyse complète d'une seule partie selon les méthodes avancées d'entropie.
"""
function analyze_single_game(
    analyzer::EntropyAnalyzer{T},
    game_data::Dict,
    game_id::Union{String, Nothing} = nothing
) where T<:AbstractFloat
    sequence = extract_index5_sequence(game_data)

    if isempty(sequence)
        return Dict("error" => "Aucune séquence INDEX5 trouvée")
    end

    # Calcul de l'évolution entropique en utilisant la fonction centralisée des métriques
    entropy_evolution = calculate_all_metrics_evolution(analyzer, sequence, 4)

    if isempty(entropy_evolution)
        return Dict("error" => "Impossible de calculer l'évolution d'entropie")
    end

    # Calcul des différentiels
    differentials = calculate_differentials(entropy_evolution)

    # Métriques finales
    final_metrics = entropy_evolution[end]

    # Statistiques de la séquence
    unique_values = Set(sequence)
    value_counts = Dict{String, Int}()
    for value in sequence
        value_counts[value] = get(value_counts, value, 0) + 1
    end

    # Résultats complets
    results = Dict(
        "game_id" => game_id,
        "sequence_length" => length(sequence),
        "unique_values_count" => length(unique_values),
        "sequence" => sequence,
        "entropy_evolution" => entropy_evolution,
        "differentials" => differentials,
        "final_metrics" => final_metrics,
        "value_counts" => value_counts,
        "analysis_summary" => Dict(
            "final_shannon_entropy" => final_metrics.simple_entropy,
            "final_theoretical_entropy" => final_metrics.simple_entropy_theoretical,
            "final_conditional_entropy" => final_metrics.conditional_entropy,
            "final_metric_entropy" => final_metrics.metric_entropy,
            "entropy_rate" => final_metrics.entropy_rate
        )
    )

    return results
end

# ═══════════════════════════════════════════════════════════════════════════════
# SYSTÈME DE PRÉDICTION
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculate_predictive_differentials(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}, position::Int) where T -> Dict{String, PredictiveDifferentials{T}}

Calcule les différentiels prédictifs pour toutes les valeurs INDEX5 possibles.
"""
function calculate_predictive_differentials(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    position::Int
) where T<:AbstractFloat
    if position < 1 || position > length(sequence)
        return Dict{String, PredictiveDifferentials{T}}()
    end

    # Calculer l'évolution entropique jusqu'à la position actuelle en utilisant la fonction centralisée
    current_sequence = sequence[1:position]
    evolution = calculate_all_metrics_evolution(analyzer, current_sequence, 4)

    if isempty(evolution)
        return Dict{String, PredictiveDifferentials{T}}()
    end

    current_metrics = evolution[end]

    # Obtenir les valeurs INDEX5 possibles selon les règles INDEX1
    current_index5 = sequence[position]
    required_index1 = calculate_required_index1(current_index5)

    if isnothing(required_index1)
        return Dict{String, PredictiveDifferentials{T}}()
    end

    valid_index5_values = get_valid_index5_values(required_index1)
    predictive_differentials = Dict{String, PredictiveDifferentials{T}}()

    # Pour chaque valeur INDEX5 possible
    for possible_index5 in valid_index5_values
        # Simuler l'ajout de cette valeur
        simulated_sequence = vcat(current_sequence, [possible_index5])

        # Calculer les nouvelles métriques simulées
        simulated_conditional = calculate_conditional_entropy(analyzer, simulated_sequence)

        # Entropie simple observée simulée
        counts = Dict{String, Int}()
        for value in simulated_sequence
            counts[value] = get(counts, value, 0) + 1
        end
        empirical_probs = T[counts[value] / length(simulated_sequence) for value in keys(counts)]
        simulated_simple = calculate_shannon_entropy(analyzer, empirical_probs)

        # Entropie théorique simulée
        simulated_theoretical = calculate_sequence_entropy_aep(analyzer, simulated_sequence)

        # T5 simulé avec la nouvelle méthode (fenêtre de 5)
        simulated_rate = if length(simulated_sequence) >= 5
            calculate_entropy_rate_new(analyzer, simulated_sequence)
        else
            current_metrics.entropy_rate
        end

        # Calculer les métriques simulées complètes pour obtenir StructEG et ConfEG
        simulated_evolution = calculate_all_metrics_evolution(analyzer, simulated_sequence, 4)
        simulated_metrics = isempty(simulated_evolution) ? current_metrics : simulated_evolution[end]

        # Calculer les différentiels absolus selon les définitions exactes en utilisant les fonctions dédiées
        diff_cond = calculate_diff_cond(simulated_conditional, current_metrics.conditional_entropy)        # DiffC = DiffCond
        diff_taux = calculate_diff_taux(simulated_rate, current_metrics.entropy_rate)                     # DiffT5 = DiffT5
        diff_div_entrop_g = calculate_diff_div_entrop_g(simulated_simple, current_metrics.simple_entropy)         # DiffDivEG = DiffDivEntropG
        diff_entrop_g = calculate_diff_entrop_g(simulated_theoretical, current_metrics.simple_entropy_theoretical)  # DiffEG = DiffEG
        diff_seg = calculate_diff_seg(simulated_metrics.struct_eg, current_metrics.struct_eg)             # DiffSEG
        diff_ceg = calculate_diff_ceg(simulated_metrics.conf_eg, current_metrics.conf_eg)                 # DiffCEG

        # Calcul du score composite selon la formule entropique optimale
        # SCORE = Mt5 × exp(-DiffEG) × exp(-DiffDivEG) × exp(-DiffC) / ((1 + DiffSEG + DiffCEG) × (1 + DiffT5))
        score = calculate_predictive_score(
            simulated_metrics.metric_entropy,   # Mt5
            diff_entrop_g,                      # DiffEG
            diff_div_entrop_g,                  # DiffDivEG
            diff_cond,                          # DiffC
            diff_seg,                           # DiffSEG
            diff_ceg,                           # DiffCEG
            diff_taux                           # DiffT5
        )

        predictive_differentials[possible_index5] = PredictiveDifferentials{T}(
            diff_cond, diff_taux, diff_div_entrop_g, diff_entrop_g, zero(T), diff_seg, diff_ceg, score
        )
    end

    return predictive_differentials
end

"""
    predict_next_index5(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> Union{String, Nothing}

Prédit la prochaine valeur INDEX5 basée sur le score composite le plus élevé.
"""
function predict_next_index5(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return nothing
    end

    position = length(sequence)
    predictive_diffs = calculate_predictive_differentials(analyzer, sequence, position)

    if isempty(predictive_diffs)
        return nothing
    end

    # Trouver la valeur avec le score le plus élevé
    best_index5 = nothing
    best_score = T(-Inf)

    for (index5, diff) in predictive_diffs
        if diff.score > best_score
            best_score = diff.score
            best_index5 = index5
        end
    end

    return best_index5
end

# ═══════════════════════════════════════════════════════════════════════════════
# AFFICHAGE ET RAPPORTS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    generate_metrics_table_report(results::Dict) -> String

Génère un rapport contenant le NOUVEAU tableau avec les 24 formules d'entropie.
"""
function generate_metrics_table_report(results::Dict)
    if haskey(results, "error")
        return "❌ Erreur: $(results["error"])"
    end

    # NOUVEAU TABLEAU AVEC LES 24 FORMULES - TOUTES LES MAINS
    sequence = results["sequence"]

    # Générer le nouveau tableau avec les 24 formules pour TOUTES les mains
    nouveau_tableau = generer_nouveau_tableau_24_formules(sequence, -1)

    # Générer le tableau des différentiels des 24 formules (main 2 à 60)
    tableau_differentiels = generer_tableau_differentiels_24_formules(sequence, -1)

    return nouveau_tableau * "\n" * tableau_differentiels
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION ANCIEN TABLEAU (DÉSACTIVÉE)
# ═══════════════════════════════════════════════════════════════════════════════
#
# function generate_metrics_table_report_OLD_DISABLED(results::Dict)
#     if haskey(results, "error")
#         return "❌ Erreur: $(results["error"])"
#     end
#
#     report = IOBuffer()
#
#     # ANCIEN EN-TÊTE (DÉSACTIVÉ)
#     println(report, "📊 MÉTRIQUES ET DIFFÉRENTIELS POUR TOUTES LES MAINS:")
#     println(report, @sprintf("%-6s %10s %10s %10s    %-15s %10s %10s %12s %10s",
#             "Main", "DiffEG", "DiffSEG", "DiffCEG", "INDEX5_Observé", "Mt5", "DiffT5", "DiffDivEG", "DiffC"))
#     println(report, "-"^119)
#
#     # ANCIENNES DONNÉES (DÉSACTIVÉES)
#     differentials = results["differentials"]
#     entropy_evolution = results["entropy_evolution"]
#     sequence = results["sequence"]
#
#     for i in 1:length(differentials)
#         diff = differentials[i]
#         metrics = entropy_evolution[i]
#         observed_index5 = i <= length(sequence) ? sequence[i] : "N/A"
#
#         @printf(report, "%-6d %10.4f %10.4f %10.4f    %-15s %10.4f %10.4f %12.4f %10.4f\n",
#                i,                                        # Main
#                diff.diff_entrop_g,                       # DiffEG (différentiel)
#                diff.diff_seg,                            # DiffSEG (différentiel)
#                diff.diff_ceg,                            # DiffCEG (différentiel)
#                observed_index5,                          # INDEX5_Observé
#                metrics.metric_entropy,                    # Mt5
#                diff.diff_taux,                           # DiffT5
#                diff.diff_div_entrop_g,                   # DiffDivEG (différentiel)
#                diff.diff_cond)                           # DiffC
#     end
#
#     println(report, "-"^119)
#
#     return String(take!(report))
# end

"""
    display_analysis_results(results::Dict)

Affiche les résultats d'analyse de manière formatée.
"""
function display_analysis_results(results::Dict)
    if haskey(results, "error")
        @error "❌ $(results["error"])"
        return
    end

    println("\n" * "="^80)
    println("📊 RÉSULTATS D'ANALYSE ENTROPIQUE")
    println("="^80)

    if haskey(results, "game_id") && !isnothing(results["game_id"])
        println("🎮 Partie: $(results["game_id"])")
    end

    println("📏 Longueur de séquence: $(results["sequence_length"])")
    println("🔢 Valeurs uniques: $(results["unique_values_count"])")

    summary = results["analysis_summary"]
    println("\n📈 MÉTRIQUES FINALES:")
    @printf("   • Entropie Shannon (observée)  : %.6f bits\n", summary["final_shannon_entropy"])
    @printf("   • Entropie AEP (théorique)     : %.6f bits\n", summary["final_theoretical_entropy"])
    @printf("   • Entropie conditionnelle      : %.6f bits\n", summary["final_conditional_entropy"])
    @printf("   • Entropie Mt5                 : %.6f bits\n", summary["final_metric_entropy"])
    @printf("   • T5                           : %.6f bits\n", summary["entropy_rate"])

    # NOUVEAU TABLEAU AVEC LES 24 FORMULES D'ENTROPIE - TOUTES LES MAINS
    sequence = results["sequence"]
    # AUCUN AFFICHAGE DANS LA CONSOLE
    # Tous les résultats sont uniquement dans le fichier de rapport partie1.txt

# ═══════════════════════════════════════════════════════════════════════════════
# ANCIEN CODE D'AFFICHAGE (DÉSACTIVÉ)
# ═══════════════════════════════════════════════════════════════════════════════
#
#     # ANCIEN AFFICHAGE (DÉSACTIVÉ)
#     differentials = results["differentials"]
#     entropy_evolution = results["entropy_evolution"]
#     sequence = results["sequence"]
#     if length(differentials) > 1
#         println("\n📊 MÉTRIQUES ET DIFFÉRENTIELS POUR TOUTES LES MAINS:")
#         println(@sprintf("%-6s %10s %10s %10s    %-15s %10s %10s %12s %10s",
#                 "Main", "DiffEG", "DiffSEG", "DiffCEG", "INDEX5_Observé", "Mt5", "DiffT5", "DiffDivEG", "DiffC"))
#         println("-"^119)
#
#         for i in 1:length(differentials)
#             diff = differentials[i]
#             metrics = entropy_evolution[i]
#             # Afficher l'INDEX5 de la main courante (Julia 1-based)
#             observed_index5 = sequence[i]  # sequence[i] correspond à la main i
#             score_str = isinf(diff.score) ? "∞" : @sprintf("%.4f", diff.score)
#
#             @printf("%-6d %10.4f %10.4f %10.4f    %-15s %10.4f %10.4f %12.4f %10.4f\n",
#                    i,                                        # Main
#                    diff.diff_entrop_g,                       # DiffEG (différentiel)
#                    diff.diff_seg,                            # DiffSEG (différentiel)
#                    diff.diff_ceg,                            # DiffCEG (différentiel)
#                    observed_index5,                          # INDEX5_Observé
#                    metrics.metric_entropy,                    # Mt5
#                    diff.diff_taux,                           # DiffT5
#                    diff.diff_div_entrop_g,                   # DiffDivEG (différentiel)
#                    diff.diff_cond)                           # DiffC
#         end
#         println("-"^119)
#     end

    println("="^80)
end



# ═══════════════════════════════════════════════════════════════════════════════
# INTERFACE PRINCIPALE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main()

Point d'entrée principal du programme avec interface interactive.
"""
function main()
    println("🎰 ANALYSEUR D'ENTROPIE BACCARAT - INDEX5 (Julia)")
    println("=" * "="^60)
    println("Basé sur les formules d'entropie de Shannon")
    println("Implémenté selon les bonnes pratiques Julia")
    println()

    # Initialisation de l'analyseur
    analyzer = EntropyAnalyzer{Float64}()

    # Chemin par défaut du fichier JSON
    default_filepath = "partie/dataset_baccarat_lupasco_20250704_092825_condensed.json"

    while true
        println("\n🎯 MENU PRINCIPAL:")
        println("1. Analyser une partie unique")
        println("2. Analyser plusieurs parties")
        println("3. Prédiction INDEX5 interactive")
        println("4. Afficher les probabilités théoriques")
        println("5. Tester les 24 formules (OBS + THEO)")

        # Vérifier la disponibilité des graphiques
        if verifier_packages_graphiques()
            println("6. Générer tous les graphiques d'une partie ✅")
        else
            println("6. Générer tous les graphiques d'une partie ❌ (packages manquants)")
        end

        println("7. Quitter")

        print("\nVotre choix (1-7): ")
        choix = readline()

        if choix == "1"
            analyze_single_game_interactive(analyzer, default_filepath)
        elseif choix == "2"
            analyze_multiple_games_interactive(analyzer, default_filepath)
        elseif choix == "3"
            interactive_prediction(analyzer)
        elseif choix == "4"
            display_theoretical_probabilities(analyzer)
        elseif choix == "5"
            tester_24_formules_interactive(default_filepath)
        elseif choix == "6"
            generer_graphiques_partie_interactive(analyzer, default_filepath)
        elseif choix == "7"
            println("👋 Au revoir! Merci d'avoir utilisé l'analyseur d'entropie Julia!")
            break
        else
            println("❌ Choix invalide!")
        end
    end
end

"""
    analyze_single_game_interactive(analyzer::EntropyAnalyzer, filepath::String)

Interface interactive pour analyser une partie unique.
"""
function analyze_single_game_interactive(analyzer::EntropyAnalyzer, filepath::String)
    println("\n📁 Chargement des données...")
    parties = load_baccarat_data(filepath)

    if isempty(parties)
        return
    end

    println("📊 $(length(parties)) parties disponibles")
    print("Numéro de la partie à analyser (1-$(length(parties))): ")

    try
        partie_num = parse(Int, readline())
        if partie_num < 1 || partie_num > length(parties)
            println("❌ Numéro de partie invalide!")
            return
        end

        game_data = parties[partie_num]
        game_id = "Partie_$partie_num"

        println("\n🔄 Analyse en cours...")
        results = analyze_single_game(analyzer, game_data, game_id)

        display_analysis_results(results)

        # Génération automatique du rapport txt avec uniquement le tableau
        if !haskey(results, "error")
            rapport_filename = "partie$(partie_num).txt"
            rapport_content = generate_metrics_table_report(results)

            try
                open(rapport_filename, "w") do file
                    write(file, rapport_content)
                end
                println("\n📄 Rapport automatiquement généré : $rapport_filename")
            catch e
                println("\n❌ Erreur lors de la génération du rapport : $e")
            end
        end

        # Proposer une prédiction
        if !haskey(results, "error")
            sequence = results["sequence"]
            prediction = predict_next_index5(analyzer, sequence)

            if !isnothing(prediction)
                println("\n🔮 PRÉDICTION PROCHAINE VALEUR: $prediction")
            end
        end

    catch e
        println("❌ Erreur: $e")
    end
end

"""
    analyze_multiple_games_interactive(analyzer::EntropyAnalyzer, filepath::String)

Interface interactive pour analyser plusieurs parties.
"""
function analyze_multiple_games_interactive(analyzer::EntropyAnalyzer, filepath::String)
    println("\n📁 Chargement des données...")
    parties = load_baccarat_data(filepath)

    if isempty(parties)
        return
    end

    println("📊 $(length(parties)) parties disponibles")
    print("Nombre de parties à analyser (max $(length(parties))): ")

    try
        num_parties = parse(Int, readline())
        if num_parties < 1 || num_parties > length(parties)
            println("❌ Nombre invalide!")
            return
        end

        println("\n🔄 Analyse de $num_parties parties en cours...")

        all_entropies = Float64[]
        all_lengths = Int[]
        successful_analyses = 0

        for i in 1:num_parties
            game_data = parties[i]
            results = analyze_single_game(analyzer, game_data, "Partie_$i")

            if !haskey(results, "error")
                push!(all_entropies, results["analysis_summary"]["final_shannon_entropy"])
                push!(all_lengths, results["sequence_length"])
                successful_analyses += 1
            end

            if i % 10 == 0
                println("   Analysé: $i/$num_parties parties")
            end
        end

        # Statistiques globales
        println("\n" * "="^80)
        println("📈 STATISTIQUES GLOBALES ($successful_analyses parties analysées)")
        println("="^80)

        if !isempty(all_entropies)
            @printf("🎯 Entropie moyenne        : %.6f ± %.6f bits\n", mean(all_entropies), std(all_entropies))
            @printf("📏 Longueur moyenne        : %.2f ± %.2f mains\n", mean(all_lengths), std(all_lengths))
            @printf("📊 Entropie min/max        : %.6f / %.6f bits\n", minimum(all_entropies), maximum(all_entropies))
            @printf("📏 Longueur min/max        : %d / %d mains\n", minimum(all_lengths), maximum(all_lengths))
        end

        println("="^80)

    catch e
        println("❌ Erreur: $e")
    end
end

"""
    interactive_prediction(analyzer::EntropyAnalyzer)

Interface interactive pour les prédictions INDEX5.
"""
function interactive_prediction(analyzer::EntropyAnalyzer)
    println("\n🔮 PRÉDICTION INDEX5 INTERACTIVE")
    println("="^50)
    println("Entrez une séquence INDEX5 (format: 0_A_BANKER,1_C_TIE,...)")
    println("Ou tapez 'quit' pour revenir au menu principal")

    while true
        print("\nSéquence INDEX5: ")
        input = strip(readline())

        if lowercase(input) == "quit"
            break
        end

        if isempty(input)
            println("❌ Séquence vide!")
            continue
        end

        # Parser la séquence
        try
            sequence = String.(strip.(split(input, ',')))

            # Valider le format INDEX5
            valid_sequence = true
            for value in sequence
                if !occursin(r"^[01]_[ABC]_(BANKER|PLAYER|TIE)$", value)
                    println("❌ Format invalide pour '$value'. Format attendu: INDEX1_INDEX2_INDEX3")
                    valid_sequence = false
                    break
                end
            end

            if !valid_sequence
                continue
            end

            println("\n📊 Séquence analysée: $(length(sequence)) valeurs")

            # Calculer la prédiction
            prediction = predict_next_index5(analyzer, sequence)

            if isnothing(prediction)
                println("❌ Impossible de calculer une prédiction")
                continue
            end

            println("🏆 PRÉDICTION: $prediction")

        catch e
            println("❌ Erreur lors du parsing: $e")
        end
    end
end

"""
    display_theoretical_probabilities(analyzer::EntropyAnalyzer)

Affiche les probabilités théoriques INDEX5.
"""
function display_theoretical_probabilities(analyzer::EntropyAnalyzer)
    println("\n📊 PROBABILITÉS THÉORIQUES INDEX5")
    println("="^60)
    println(@sprintf("%-15s %12s %8s", "INDEX5", "Probabilité", "Pourcentage"))
    println("-"^60)

    # Trier par probabilité décroissante
    sorted_probs = sort(collect(analyzer.theoretical_probs), by = x -> x[2], rev = true)

    total_prob = 0.0
    for (index5, prob) in sorted_probs
        println(@sprintf("%-15s %12.6f %8.4f%%", index5, prob, prob * 100))
        total_prob += prob
    end

    println("-"^60)
    println(@sprintf("%-15s %12.6f %8.4f%%", "TOTAL", total_prob, total_prob * 100))
    println("="^60)

    # Statistiques
    probs = collect(values(analyzer.theoretical_probs))
    println("\n📈 STATISTIQUES:")
    @printf("   • Moyenne     : %.6f\n", mean(probs))
    @printf("   • Écart-type  : %.6f\n", std(probs))
    @printf("   • Min/Max     : %.6f / %.6f\n", minimum(probs), maximum(probs))
    @printf("   • Entropie max: %.6f bits (distribution uniforme)\n", log2(length(probs)))

    # Calculer l'entropie de la distribution théorique
    theoretical_entropy = -sum(p * log2(p) for p in probs)
    @printf("   • Entropie réelle: %.6f bits\n", theoretical_entropy)
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION DÉDIÉE - FORMULES DÉSACTIVÉES
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section contient TOUTES les formules mathématiques du programme
# qui ont été temporairement désactivées selon les instructions.
#
# ORGANISATION DES FORMULES :
# 1. Formules utilitaires de base
# 2. Formules d'entropie de Shannon
# 3. Formules AEP (Asymptotic Equipartition Property)
# 4. Formules de calcul Mt5 (Kolmogorov-Sinai)
# 5. Formules de calcul T5 (Entropy Rate)
# 6. Formules d'entropie conditionnelle
# 7. Formules de conformité et structure entropique
# 8. Formules de différentiels entropiques
# 9. Formule de score composite prédictif
# 10. Formules de calcul de blocs d'entropie
#
# ═══════════════════════════════════════════════════════════════════════════════

# ─────────────────────────────────────────────────────────────────────────────
# 1. FORMULES UTILITAIRES DE BASE
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de safe_log :
# return log(x) / log(base)

# FORMULE ORIGINALE de validate_probabilities :
# total = sum(clean_probs)
# if total ≈ zero(T)
#     throw(ArgumentError("All probabilities are zero"))
# end
# return clean_probs ./ total

# ─────────────────────────────────────────────────────────────────────────────
# 2. FORMULES D'ENTROPIE DE SHANNON
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_shannon_entropy :
# entropy = zero(T)
# for p in probs
#     if p > zero(T)
#         entropy -= p * safe_log(p, analyzer.base, analyzer.epsilon)
#     end
# end
# return entropy

# ─────────────────────────────────────────────────────────────────────────────
# 3. FORMULES AEP (ASYMPTOTIC EQUIPARTITION PROPERTY)
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_sequence_entropy_aep :
# total_log_prob = zero(T)
# for value in sequence
#     p_theo = get(analyzer.theoretical_probs, value, zero(T))
#     total_log_prob += safe_log(p_theo, analyzer.base, analyzer.epsilon)
# end
# return -total_log_prob / length(sequence)

# FORMULE ORIGINALE de calculate_sequence_entropy_aep_observed :
# counts = Dict{String, Int}()
# for value in sequence
#     counts[value] = get(counts, value, 0) + 1
# end
# n = length(sequence)
# total_log_prob = zero(T)
# for value in sequence
#     p_obs = T(counts[value]) / T(n)  # Probabilité observée
#     total_log_prob += safe_log(p_obs, analyzer.base, analyzer.epsilon)
# end
# return -total_log_prob / n

# ─────────────────────────────────────────────────────────────────────────────
# 4. FORMULES DE CALCUL Mt5 (KOLMOGOROV-SINAI)
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_metric_from_t5 :
# log2_18 = log(T(18)) / log(analyzer.base)  # log₂(18) calculé exactement
# if log2_18 ≈ zero(T)
#     return zero(T)
# end
# return t5_value / log2_18

# FORMULE ORIGINALE de calculate_metric_entropy_new :
# t5 = calculate_entropy_rate_new(analyzer, sequence)
# return calculate_metric_from_t5(analyzer, t5)

# FORMULE ORIGINALE de estimate_metric_entropy :
# max_length = length(block_entropies)
# return block_entropies[end] / max_length

# ─────────────────────────────────────────────────────────────────────────────
# 5. FORMULES DE CALCUL T5 (ENTROPY RATE)
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_entropy_rate_new :
# window_5 = sequence[(end-4):end]
# h5 = calculate_sequence_entropy_aep(analyzer, window_5)
# return h5

# FORMULE ORIGINALE de calculate_entropy_rate :
# return isempty(block_entropies) ? zero(T) : block_entropies[end]

# ─────────────────────────────────────────────────────────────────────────────
# 6. FORMULES D'ENTROPIE CONDITIONNELLE
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_conditional_entropy :
# context_transitions = Dict{String, Dict{String, Int}}()
# for i in 1:(length(sequence)-1)
#     context = sequence[i]
#     next_symbol = sequence[i+1]
#     if !haskey(context_transitions, context)
#         context_transitions[context] = Dict{String, Int}()
#     end
#     context_transitions[context][next_symbol] =
#         get(context_transitions[context], next_symbol, 0) + 1
# end
# total_transitions = sum(sum(values(transitions)) for transitions in values(context_transitions))
# conditional_entropy = zero(T)
# for (context, transitions) in context_transitions
#     context_prob = T(sum(values(transitions))) / T(total_transitions)
#     context_sequence = String[]
#     for (next_symbol, count) in transitions
#         append!(context_sequence, fill(next_symbol, count))
#     end
#     context_entropy = calculate_sequence_entropy_aep(analyzer, context_sequence)
#     conditional_entropy += context_prob * context_entropy
# end
# return conditional_entropy

# ─────────────────────────────────────────────────────────────────────────────
# 7. FORMULES DE CONFORMITÉ ET STRUCTURE ENTROPIQUE
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_conf_eg :
# return abs(entrop_g - eg_obs)

# FORMULE ORIGINALE de calculate_struct_eg :
# if eg_obs ≈ zero(T)
#     return T(Inf)  # Division par zéro -> infini
# end
# return entrop_g / eg_obs

# ─────────────────────────────────────────────────────────────────────────────
# 8. FORMULES DE DIFFÉRENTIELS ENTROPIQUES
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_diff_cond :
# return abs(current_conditional - previous_conditional)

# FORMULE ORIGINALE de calculate_diff_taux :
# return abs(current_rate - previous_rate)

# FORMULE ORIGINALE de calculate_diff_div_entrop_g :
# return abs(current_simple - previous_simple)

# FORMULE ORIGINALE de calculate_diff_entrop_g :
# return abs(current_theoretical - previous_theoretical)

# FORMULE ORIGINALE de calculate_diff_egobs :
# return abs(current_egobs - previous_egobs)

# FORMULE ORIGINALE de calculate_diff_seg :
# return abs(current_struct_eg - previous_struct_eg)

# FORMULE ORIGINALE de calculate_diff_ceg :
# return abs(current_conf_eg - previous_conf_eg)

# ─────────────────────────────────────────────────────────────────────────────
# 9. FORMULE DE SCORE COMPOSITE PRÉDICTIF
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_predictive_score :
# exp_diff_eg = exp(-diff_eg)
# exp_diff_div_eg = exp(-diff_div_eg)
# exp_diff_c = exp(-diff_c)
# denominator = (one(T) + diff_seg + diff_ceg) * (one(T) + diff_t5)
# if denominator ≈ zero(T)
#     return T(Inf)  # Score infini
# else
#     numerator = mt5 * exp_diff_eg * exp_diff_div_eg * exp_diff_c
#     return numerator / denominator
# end

# ─────────────────────────────────────────────────────────────────────────────
# 10. FORMULES DE CALCUL DE BLOCS D'ENTROPIE
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_block_entropies :
# for block_len in 1:min(max_length, length(sequence))
#     if block_len == 1
#         block_entropy = calculate_sequence_entropy_aep(analyzer, sequence)
#     else
#         total_entropy = zero(T)
#         num_blocks = 0
#         for i in 1:(length(sequence) - block_len + 1)
#             block_sequence = sequence[i:(i + block_len - 1)]
#             total_entropy += calculate_sequence_entropy_aep(analyzer, block_sequence)
#             num_blocks += 1
#         end
#         block_entropy = num_blocks > 0 ? total_entropy / num_blocks : zero(T)
#     end
#     push!(entropies, block_entropy)
# end

# ═══════════════════════════════════════════════════════════════════════════════
# FIN DE LA SECTION DÉDIÉE - FORMULES DÉSACTIVÉES
# ═══════════════════════════════════════════════════════════════════════════════
#
# RÉSUMÉ DES FORMULES DÉSACTIVÉES :
# ✅ 10 catégories de formules mathématiques identifiées et désactivées
# ✅ Toutes les formules conservées dans cette section dédiée
# ✅ Valeurs par défaut temporaires assignées dans les fonctions
# ✅ Code syntaxiquement correct maintenu
# ✅ Documentation complète des formules originales
#
# TOTAL : Toutes les formules du programme regroupées et désactivées
# ═══════════════════════════════════════════════════════════════════════════════

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS DE GÉNÉRATION DE GRAPHIQUES COMPLETS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    generer_tous_graphiques(graphique::GraphiqueEntropique{T}, sequence::Vector{String}) where T

Génère tous les graphiques révélateurs avec les 48 métriques complètes.
Crée 10 graphiques essentiels dans le dossier de la partie.

# Arguments
- `graphique::GraphiqueEntropique{T}`: Générateur de graphiques
- `sequence::Vector{String}`: Séquence INDEX5 de la partie

# Graphiques générés
1. Heatmap maîtresse 48x60
2. Analyse en composantes principales 3D
3. Matrice de corrélation 48x48
4. Graphique temporel multi-panneaux
5. Radar géant 48-axes
6. Graphique de prédiction INDEX5
7. Graphique de flux entropique
8. Dashboard de détection de régimes
9. Signature évolutive 3D
10. Méga-dashboard interactif
"""
function generer_tous_graphiques(graphique::GraphiqueEntropique{T}, sequence::Vector{String}) where T
    println("🎨 Génération de tous les graphiques pour $(graphique.partie_name)...")

    # Calculer toutes les métriques pour tous les points
    metriques_completes = calculer_toutes_metriques_completes(sequence)

    # 1. Heatmap maîtresse 48x60
    generer_heatmap_maitresse(graphique, metriques_completes, sequence)

    # 2. Analyse PCA 3D
    generer_pca_3d(graphique, metriques_completes, sequence)

    # 3. Matrice de corrélation 48x48
    generer_matrice_correlation(graphique, metriques_completes)

    # 4. Graphique temporel multi-panneaux
    generer_temporel_multi_panneaux(graphique, metriques_completes, sequence)

    # 5. Radar géant 48-axes
    generer_radar_geant(graphique, metriques_completes, sequence)

    # 6. Graphique de prédiction INDEX5
    generer_prediction_index5(graphique, metriques_completes, sequence)

    # 7. Graphique de flux entropique
    generer_flux_entropique(graphique, metriques_completes, sequence)

    # 8. Dashboard de détection de régimes
    generer_dashboard_regimes(graphique, metriques_completes, sequence)

    # 9. Signature évolutive 3D
    generer_signature_evolutive_3d(graphique, metriques_completes, sequence)

    # 10. Méga-dashboard interactif
    generer_mega_dashboard(graphique, metriques_completes, sequence)

    println("✅ Tous les graphiques générés dans le dossier: $(graphique.output_dir)")
end

"""
    calculer_toutes_metriques_completes(sequence::Vector{String}) -> Dict

Calcule toutes les 48 métriques (24 principales + 24 différentiels) pour chaque main.
Retourne un dictionnaire avec les matrices de données complètes.

# Returns
- `Dict`: Dictionnaire contenant toutes les métriques organisées
"""
function calculer_toutes_metriques_completes(sequence::Vector{String})
    n_mains = length(sequence)

    # Initialiser les structures
    formulas_obs = FormulasObserved{Float64}(2.0, 1e-10)
    formulas_theo = FormulasTheoretical{Float64}(2.0, 1e-10)
    differentials = DifferentialMetrics{Float64}(2, n_mains, 2.0)

    # Matrices pour stocker toutes les métriques
    metriques_principales = zeros(Float64, 24, n_mains)
    metriques_differentiels = zeros(Float64, 24, n_mains-1)

    # Noms des métriques dans l'ordre
    noms_principales = [
        "ShannonT", "AEPT", "TauxT", "MetricT", "CondT", "DivKLT",
        "InfoMutT", "CrossT", "TopoT", "BlockT", "CondDecT", "TheoAEPT",
        "ShannonO", "AEPO", "TauxO", "MetricO", "CondO", "DivKLO",
        "InfoMutO", "CrossO", "TopoO", "BlockO", "CondDecO", "TheoAEPO"
    ]

    noms_differentiels = [
        "DiffShanT", "DiffAEPT", "DiffTauxT", "DiffMetrT", "DiffCondT", "DiffDivKT",
        "DiffInfoT", "DiffCroT", "DiffTopoT", "DiffBlocT", "DiffConDT", "DiffThAET",
        "DiffShanO", "DiffAEPO", "DiffTauxO", "DiffMetrO", "DiffCondO", "DiffDivKO",
        "DiffInfoO", "DiffCroO", "DiffTopoO", "DiffBlocO", "DiffConDO", "DiffThAEO"
    ]

    # Calculer les métriques pour chaque main
    for n in 1:n_mains
        metriques = calculer_toutes_metriques(formulas_obs, formulas_theo, sequence, n)

        # Stocker les métriques principales (metriques est un Tuple de 24 éléments)
        # Théoriques (1-12)
        metriques_principales[1, n] = metriques[1]   # shannon_t
        metriques_principales[2, n] = metriques[2]   # aep_t
        metriques_principales[3, n] = metriques[3]   # taux_t
        metriques_principales[4, n] = metriques[4]   # metric_t
        metriques_principales[5, n] = metriques[5]   # cond_t
        metriques_principales[6, n] = metriques[6]   # divkl_t
        metriques_principales[7, n] = metriques[7]   # infomut_t
        metriques_principales[8, n] = metriques[8]   # cross_t
        metriques_principales[9, n] = metriques[9]   # topo_t
        metriques_principales[10, n] = metriques[10] # block_t
        metriques_principales[11, n] = metriques[11] # conddec_t
        metriques_principales[12, n] = metriques[12] # theoaep_t

        # Observées (13-24)
        metriques_principales[13, n] = metriques[13] # shannon_o
        metriques_principales[14, n] = metriques[14] # aep_o
        metriques_principales[15, n] = metriques[15] # taux_o
        metriques_principales[16, n] = metriques[16] # metric_o
        metriques_principales[17, n] = metriques[17] # cond_o
        metriques_principales[18, n] = metriques[18] # divkl_o
        metriques_principales[19, n] = metriques[19] # infomut_o
        metriques_principales[20, n] = metriques[20] # cross_o
        metriques_principales[21, n] = metriques[21] # topo_o
        metriques_principales[22, n] = metriques[22] # block_o
        metriques_principales[23, n] = metriques[23] # conddec_o
        metriques_principales[24, n] = metriques[24] # theoaep_o
    end

    # Calculer les différentiels
    calculer_differentiels!(differentials, formulas_obs, formulas_theo, sequence)

    # Stocker les différentiels
    for i in 1:(n_mains-1)
        metriques_differentiels[1, i] = differentials.diff_shannon_t[i]
        metriques_differentiels[2, i] = differentials.diff_aep_t[i]
        metriques_differentiels[3, i] = differentials.diff_taux_t[i]
        metriques_differentiels[4, i] = differentials.diff_metric_t[i]
        metriques_differentiels[5, i] = differentials.diff_cond_t[i]
        metriques_differentiels[6, i] = differentials.diff_divkl_t[i]
        metriques_differentiels[7, i] = differentials.diff_infomut_t[i]
        metriques_differentiels[8, i] = differentials.diff_cross_t[i]
        metriques_differentiels[9, i] = differentials.diff_topo_t[i]
        metriques_differentiels[10, i] = differentials.diff_block_t[i]
        metriques_differentiels[11, i] = differentials.diff_conddec_t[i]
        metriques_differentiels[12, i] = differentials.diff_theoaep_t[i]

        metriques_differentiels[13, i] = differentials.diff_shannon_o[i]
        metriques_differentiels[14, i] = differentials.diff_aep_o[i]
        metriques_differentiels[15, i] = differentials.diff_taux_o[i]
        metriques_differentiels[16, i] = differentials.diff_metric_o[i]
        metriques_differentiels[17, i] = differentials.diff_cond_o[i]
        metriques_differentiels[18, i] = differentials.diff_divkl_o[i]
        metriques_differentiels[19, i] = differentials.diff_infomut_o[i]
        metriques_differentiels[20, i] = differentials.diff_cross_o[i]
        metriques_differentiels[21, i] = differentials.diff_topo_o[i]
        metriques_differentiels[22, i] = differentials.diff_block_o[i]
        metriques_differentiels[23, i] = differentials.diff_conddec_o[i]
        metriques_differentiels[24, i] = differentials.diff_theoaep_o[i]
    end

    return Dict(
        "principales" => metriques_principales,
        "differentiels" => metriques_differentiels,
        "noms_principales" => noms_principales,
        "noms_differentiels" => noms_differentiels,
        "n_mains" => n_mains
    )
end

"""
    generer_heatmap_maitresse(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T

Génère la heatmap maîtresse 48x60 avec clustering intelligent.
Le cerveau du système de visualisation.
"""
function generer_heatmap_maitresse(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T
    println("📊 Génération de la heatmap maîtresse 48x60...")

    try
        # Combiner toutes les métriques
        principales = metriques["principales"]
        differentiels = metriques["differentiels"]
        n_mains = metriques["n_mains"]

        # Créer la matrice complète 48x60
        matrice_complete = zeros(Float64, 48, n_mains)
        matrice_complete[1:24, :] = principales
        matrice_complete[25:48, 2:end] = differentiels  # Différentiels commencent à la main 2

        # Normalisation simple (0-1)
        for i in 1:48
            row = matrice_complete[i, :]
            min_val, max_val = extrema(row)
            if max_val > min_val
                matrice_complete[i, :] = (row .- min_val) ./ (max_val - min_val)
            end
        end

        # Noms complets des métriques
        noms_complets = vcat(metriques["noms_principales"], metriques["noms_differentiels"])

        # Créer la heatmap simple
        p = Plots.heatmap(matrice_complete,
            title="HEATMAP MAITRESSE - 48 Metriques Entropiques - $(graphique.partie_name)",
            size=(1200, 800)
        )

        # Sauvegarder
        Plots.savefig(p, joinpath(graphique.output_dir, "01_heatmap_maitresse_48x60.png"))
        println("✅ Heatmap maîtresse sauvegardée")

    catch e
        println("❌ Erreur lors de la génération de la heatmap: $e")
        # Créer un graphique simple de fallback
        p = Plots.plot([1, 2, 3], [1, 4, 2], title="Graphique de test - $(graphique.partie_name)")
        Plots.savefig(p, joinpath(graphique.output_dir, "01_test_simple.png"))
        println("✅ Graphique de test sauvegardé")
    end
end

"""
    generer_pca_3d(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T

Génère l'analyse en composantes principales 3D dynamique.
Réduction de la complexité 48D vers 3D.
"""
function generer_pca_3d(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T
    println("📊 Génération de l'analyse PCA 3D...")

    # Préparer les données pour PCA
    principales = metriques["principales"]
    n_mains = metriques["n_mains"]

    # Transposer pour avoir les observations en lignes
    data_pca = transpose(principales)

    # Normaliser les données
    data_normalized = (data_pca .- mean(data_pca, dims=1)) ./ std(data_pca, dims=1)

    # Calculer PCA
    pca_model = fit(PCA, transpose(data_normalized), maxoutdim=3)
    transformed = MultivariateStats.transform(pca_model, transpose(data_normalized))

    # Créer les couleurs selon INDEX5
    index5_unique = unique(sequence)
    colors = [findfirst(x -> x == seq, index5_unique) for seq in sequence]

    # Graphique 3D
    p = scatter3d(transformed[1, :], transformed[2, :], transformed[3, :],
        color=colors,
        title="🎯 ANALYSE PCA 3D - Trajectoire Entropique\nPartie: $(graphique.partie_name)",
        xlabel="PC1 ($(round(principalvars(pca_model)[1]/sum(principalvars(pca_model))*100, digits=1))%)",
        ylabel="PC2 ($(round(principalvars(pca_model)[2]/sum(principalvars(pca_model))*100, digits=1))%)",
        zlabel="PC3 ($(round(principalvars(pca_model)[3]/sum(principalvars(pca_model))*100, digits=1))%)",
        size=graphique.resolution,
        dpi=graphique.dpi,
        markersize=4,
        alpha=0.7
    )

    # Ajouter la trajectoire temporelle
    plot3d!(transformed[1, :], transformed[2, :], transformed[3, :],
        color=:black, linewidth=2, alpha=0.5, label="Trajectoire temporelle")

    # Sauvegarder
    savefig(p, joinpath(graphique.output_dir, "02_pca_3d_trajectoire.png"))
    println("✅ Analyse PCA 3D sauvegardée")
end

"""
    generer_matrice_correlation(graphique::GraphiqueEntropique{T}, metriques::Dict) where T

Génère la matrice de corrélation géante 48x48.
L'analyseur de relations entre toutes les métriques.
"""
function generer_matrice_correlation(graphique::GraphiqueEntropique{T}, metriques::Dict) where T
    println("📊 Génération de la matrice de corrélation 48x48...")

    # Combiner toutes les métriques
    principales = metriques["principales"]
    differentiels = metriques["differentiels"]
    n_mains = metriques["n_mains"]

    # Créer la matrice complète pour corrélation
    matrice_complete = zeros(Float64, 48, n_mains-1)  # Utiliser n_mains-1 pour aligner avec les différentiels
    matrice_complete[1:24, :] = principales[:, 2:end]  # Exclure la première main pour aligner
    matrice_complete[25:48, :] = differentiels

    # Calculer la matrice de corrélation
    correlation_matrix = cor(transpose(matrice_complete))

    # Noms complets
    noms_complets = vcat(metriques["noms_principales"], metriques["noms_differentiels"])

    # Créer la heatmap de corrélation
    p = heatmap(noms_complets, noms_complets, correlation_matrix,
        title="🔗 MATRICE DE CORRÉLATION 48x48\nPartie: $(graphique.partie_name)",
        color=:RdBu_r,
        size=(1400, 1400),
        dpi=graphique.dpi,
        aspect_ratio=:equal
    )

    # Rotation des labels pour lisibilité
    plot!(xrotation=45, yrotation=45)

    # Sauvegarder
    savefig(p, joinpath(graphique.output_dir, "03_matrice_correlation_48x48.png"))
    println("✅ Matrice de corrélation 48x48 sauvegardée")
end

"""
    generer_temporel_multi_panneaux(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T

Génère le graphique temporel multi-panneaux complet.
Grid 8x6 = 48 subplots pour toutes les métriques.
"""
function generer_temporel_multi_panneaux(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T
    println("📊 Génération du graphique temporel multi-panneaux...")

    principales = metriques["principales"]
    differentiels = metriques["differentiels"]
    n_mains = metriques["n_mains"]
    noms_principales = metriques["noms_principales"]
    noms_differentiels = metriques["noms_differentiels"]

    # Créer les subplots pour les métriques principales
    plots_principales = []
    for i in 1:24
        p = plot(1:n_mains, principales[i, :],
            title=noms_principales[i],
            xlabel="Main",
            ylabel="Valeur",
            linewidth=2,
            color=:blue,
            legend=false,
            titlefontsize=8,
            tickfontsize=6
        )
        push!(plots_principales, p)
    end

    # Créer les subplots pour les différentiels
    plots_differentiels = []
    for i in 1:24
        p = plot(2:n_mains, differentiels[i, :],
            title=noms_differentiels[i],
            xlabel="Main",
            ylabel="Diff",
            linewidth=2,
            color=:red,
            legend=false,
            titlefontsize=8,
            tickfontsize=6
        )
        push!(plots_differentiels, p)
    end

    # Combiner tous les plots
    all_plots = vcat(plots_principales, plots_differentiels)

    # Créer le layout 8x6
    p_combined = plot(all_plots...,
        layout=(8, 6),
        size=(2400, 1600),
        dpi=graphique.dpi,
        plot_title="📈 ÉVOLUTION TEMPORELLE - 48 Métriques\nPartie: $(graphique.partie_name)"
    )

    # Sauvegarder
    savefig(p_combined, joinpath(graphique.output_dir, "04_temporel_multi_panneaux_48.png"))
    println("✅ Graphique temporel multi-panneaux sauvegardé")
end

"""
    generer_radar_geant(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T

Génère le radar géant 48-axes.
Profil entropique complet à différents moments.
"""
function generer_radar_geant(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T
    println("📊 Génération du radar géant 48-axes...")

    principales = metriques["principales"]
    differentiels = metriques["differentiels"]
    n_mains = metriques["n_mains"]
    noms_complets = vcat(metriques["noms_principales"], metriques["noms_differentiels"])

    # Sélectionner 4 mains représentatives
    mains_cles = [min(10, n_mains), min(20, n_mains), min(40, n_mains), n_mains]
    couleurs = [:blue, :green, :orange, :red]
    labels = ["Main $(m)" for m in mains_cles]

    # Préparer les données normalisées (0-1)
    data_radar = []
    for (idx, main) in enumerate(mains_cles)
        # Combiner principales et différentiels pour cette main
        if main == 1
            # Pour la main 1, pas de différentiels
            valeurs = vcat(principales[:, main], zeros(24))
        else
            valeurs = vcat(principales[:, main], differentiels[:, main-1])
        end

        # Normaliser entre 0 et 1
        valeurs_norm = (valeurs .- minimum(valeurs)) ./ (maximum(valeurs) - minimum(valeurs) + 1e-10)
        push!(data_radar, valeurs_norm)
    end

    # Créer le graphique radar (approximation avec plot polaire)
    angles = range(0, 2π, length=49)[1:48]  # 48 angles pour 48 métriques

    p = plot(title="🎯 RADAR GÉANT 48-AXES\nPartie: $(graphique.partie_name)",
        size=graphique.resolution,
        dpi=graphique.dpi,
        projection=:polar
    )

    for (idx, (data, couleur, label)) in enumerate(zip(data_radar, couleurs, labels))
        # Fermer le polygone
        angles_closed = vcat(angles, angles[1])
        data_closed = vcat(data, data[1])

        plot!(angles_closed, data_closed,
            label=label,
            color=couleur,
            linewidth=2,
            alpha=0.7
        )
    end

    # Sauvegarder
    savefig(p, joinpath(graphique.output_dir, "05_radar_geant_48_axes.png"))
    println("✅ Radar géant 48-axes sauvegardé")
end

"""
    generer_prediction_index5(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T

Génère le graphique de prédiction INDEX5.
L'objectif final : prédire INDEX5 avec les 48 métriques.
"""
function generer_prediction_index5(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T
    println("📊 Génération du graphique de prédiction INDEX5...")

    principales = metriques["principales"]
    n_mains = metriques["n_mains"]

    # Analyser la distribution des INDEX5
    index5_counts = Dict{String, Int}()
    for idx5 in sequence
        index5_counts[idx5] = get(index5_counts, idx5, 0) + 1
    end

    # Créer le graphique de distribution INDEX5
    index5_labels = collect(keys(index5_counts))
    index5_values = collect(values(index5_counts))

    p1 = bar(index5_labels, index5_values,
        title="Distribution INDEX5 Observée",
        xlabel="INDEX5",
        ylabel="Fréquence",
        color=:lightblue,
        xrotation=45
    )

    # Analyser la corrélation entre métriques et INDEX5
    # Utiliser InfoMutO comme exemple de métrique prédictive
    info_mut_o = principales[19, :]  # InfoMutO est à l'index 19

    p2 = scatter(info_mut_o, 1:n_mains,
        title="InfoMutO vs Temps",
        xlabel="InfoMutO",
        ylabel="Main",
        color=:red,
        alpha=0.6
    )

    # Évolution temporelle des métriques clés
    metriques_cles = [1, 13, 7, 19]  # ShannonT, ShannonO, InfoMutT, InfoMutO
    noms_cles = ["ShannonT", "ShannonO", "InfoMutT", "InfoMutO"]

    p3 = plot(title="Métriques Clés vs Temps")
    for (idx, nom) in zip(metriques_cles, noms_cles)
        plot!(1:n_mains, principales[idx, :],
            label=nom,
            linewidth=2
        )
    end
    xlabel!("Main")
    ylabel!("Valeur")

    # Score de prédictibilité composite
    # Combinaison simple : (1-CondO) * InfoMutO * (1/ShannonO)
    cond_o = principales[17, :]  # CondO
    shannon_o = principales[13, :]  # ShannonO

    score_predictibilite = (1.0 .- cond_o) .* info_mut_o ./ (shannon_o .+ 1e-10)

    p4 = plot(1:n_mains, score_predictibilite,
        title="Score de Prédictibilité Composite",
        xlabel="Main",
        ylabel="Score",
        linewidth=2,
        color=:purple
    )

    # Combiner les graphiques
    p_combined = plot(p1, p2, p3, p4,
        layout=(2, 2),
        size=(1600, 1200),
        dpi=graphique.dpi,
        plot_title="🎯 ANALYSE PRÉDICTIVE INDEX5\nPartie: $(graphique.partie_name)"
    )

    # Sauvegarder
    savefig(p_combined, joinpath(graphique.output_dir, "06_prediction_index5.png"))
    println("✅ Graphique de prédiction INDEX5 sauvegardé")
end

"""
    generer_flux_entropique(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T

Génère le graphique de flux entropique complet.
Visualisation des relations entre métriques théoriques et observées.
"""
function generer_flux_entropique(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T
    println("📊 Génération du graphique de flux entropique...")

    principales = metriques["principales"]
    n_mains = metriques["n_mains"]

    # Calculer les corrélations entre métriques théoriques et observées
    correlations = []
    noms_correlations = []

    for i in 1:12
        corr_val = cor(principales[i, :], principales[i+12, :])
        push!(correlations, abs(corr_val))
        push!(noms_correlations, "$(metriques["noms_principales"][i])")
    end

    # Graphique en barres des corrélations
    p1 = bar(noms_correlations, correlations,
        title="Corrélations Théorique-Observé",
        xlabel="Métriques",
        ylabel="Corrélation |r|",
        color=:lightgreen,
        xrotation=45
    )

    # Graphique de flux (approximation avec scatter)
    p2 = scatter(title="Flux Entropique Théorique → Observé")
    for i in 1:12
        scatter!(principales[i, :], principales[i+12, :],
            label=metriques["noms_principales"][i][1:min(8, end)],
            alpha=0.6,
            markersize=3
        )
    end
    xlabel!("Valeurs Théoriques")
    ylabel!("Valeurs Observées")

    # Évolution des ratios Théorique/Observé
    p3 = plot(title="Ratios Théorique/Observé")
    for i in 1:12
        ratio = principales[i, :] ./ (principales[i+12, :] .+ 1e-10)
        plot!(1:n_mains, ratio,
            label=metriques["noms_principales"][i][1:min(6, end)],
            linewidth=1,
            alpha=0.7
        )
    end
    xlabel!("Main")
    ylabel!("Ratio T/O")

    # Heatmap des corrélations croisées
    corr_matrix = cor(transpose(principales))
    p4 = heatmap(corr_matrix[1:12, 13:24],
        title="Corrélations Croisées T-O",
        color=:RdBu_r,
        aspect_ratio=:equal
    )

    # Combiner
    p_combined = plot(p1, p2, p3, p4,
        layout=(2, 2),
        size=(1600, 1200),
        dpi=graphique.dpi,
        plot_title="🌊 FLUX ENTROPIQUE COMPLET\nPartie: $(graphique.partie_name)"
    )

    # Sauvegarder
    savefig(p_combined, joinpath(graphique.output_dir, "07_flux_entropique.png"))
    println("✅ Graphique de flux entropique sauvegardé")
end

"""
    generer_dashboard_regimes(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T

Génère le dashboard de détection de régimes complet.
Détecteur de changements avec les différentiels.
"""
function generer_dashboard_regimes(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T
    println("📊 Génération du dashboard de détection de régimes...")

    differentiels = metriques["differentiels"]
    n_mains = metriques["n_mains"]

    # Sélectionner les différentiels clés
    diff_shannon_o = differentiels[13, :]  # DiffShanO
    diff_info_mut_o = differentiels[19, :]  # DiffInfoO
    diff_cond_o = differentiels[17, :]  # DiffCondO
    diff_divkl_o = differentiels[18, :]  # DiffDivKO

    # Graphique des différentiels clés
    p1 = plot(title="Différentiels Clés (Détecteurs de Régime)")
    plot!(2:n_mains, diff_shannon_o, label="DiffShanO", linewidth=2, color=:blue)
    plot!(2:n_mains, diff_info_mut_o, label="DiffInfoO", linewidth=2, color=:red)
    plot!(2:n_mains, diff_cond_o, label="DiffCondO", linewidth=2, color=:green)
    plot!(2:n_mains, diff_divkl_o, label="DiffDivKO", linewidth=2, color=:orange)
    xlabel!("Main")
    ylabel!("Différentiel")

    # Score de volatilité composite
    volatilite = sqrt.(diff_shannon_o.^2 + diff_info_mut_o.^2 + diff_cond_o.^2 + diff_divkl_o.^2)

    p2 = plot(2:n_mains, volatilite,
        title="Score de Volatilité Composite",
        xlabel="Main",
        ylabel="Volatilité",
        linewidth=3,
        color=:purple
    )

    # Détection automatique des pics (changements de régime)
    seuil_volatilite = mean(volatilite) + 2*std(volatilite)
    pics = findall(x -> x > seuil_volatilite, volatilite)

    if !isempty(pics)
        scatter!(pics .+ 1, volatilite[pics],
            color=:red,
            markersize=8,
            label="Changements de Régime"
        )
    end

    # Heatmap des différentiels
    p3 = heatmap(2:n_mains, metriques["noms_differentiels"], differentiels,
        title="Heatmap Complète des Différentiels",
        color=:RdBu_r,
        xlabel="Main",
        ylabel="Différentiels"
    )

    # Analyse des régimes
    # Diviser en zones selon la volatilité
    zones_regime = ones(Int, length(volatilite))
    for (i, vol) in enumerate(volatilite)
        if vol > seuil_volatilite
            zones_regime[i] = 3  # Régime chaotique
        elseif vol > mean(volatilite)
            zones_regime[i] = 2  # Régime transitoire
        else
            zones_regime[i] = 1  # Régime stable
        end
    end

    p4 = plot(2:n_mains, zones_regime,
        title="Classification des Régimes",
        xlabel="Main",
        ylabel="Type de Régime",
        linewidth=3,
        color=:viridis,
        yticks=([1, 2, 3], ["Stable", "Transitoire", "Chaotique"])
    )

    # Combiner
    p_combined = plot(p1, p2, p3, p4,
        layout=(2, 2),
        size=(1600, 1200),
        dpi=graphique.dpi,
        plot_title="🚨 DASHBOARD DÉTECTION DE RÉGIMES\nPartie: $(graphique.partie_name)"
    )

    # Sauvegarder
    savefig(p_combined, joinpath(graphique.output_dir, "08_dashboard_regimes.png"))
    println("✅ Dashboard de détection de régimes sauvegardé")
end

"""
    generer_signature_evolutive_3d(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T

Génère la signature évolutive 3D.
Trajectoire dans l'espace théorique-observé-dynamique.
"""
function generer_signature_evolutive_3d(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T
    println("📊 Génération de la signature évolutive 3D...")

    principales = metriques["principales"]
    differentiels = metriques["differentiels"]
    n_mains = metriques["n_mains"]

    # Calculer les composantes principales pour chaque groupe
    # PC1 des métriques théoriques (1-12)
    theo_data = transpose(principales[1:12, :])
    theo_normalized = (theo_data .- mean(theo_data, dims=1)) ./ std(theo_data, dims=1)
    pca_theo = fit(PCA, transpose(theo_normalized), maxoutdim=1)
    pc1_theo = MultivariateStats.transform(pca_theo, transpose(theo_normalized))[1, :]

    # PC1 des métriques observées (13-24)
    obs_data = transpose(principales[13:24, :])
    obs_normalized = (obs_data .- mean(obs_data, dims=1)) ./ std(obs_data, dims=1)
    pca_obs = fit(PCA, transpose(obs_normalized), maxoutdim=1)
    pc1_obs = MultivariateStats.transform(pca_obs, transpose(obs_normalized))[1, :]

    # PC1 des différentiels (moyenne des différentiels pour chaque main)
    diff_mean = vec(mean(differentiels, dims=1))
    # Étendre pour avoir la même longueur que les autres
    pc1_diff = vcat([0.0], diff_mean)  # Ajouter 0 pour la main 1

    # Créer les couleurs selon INDEX5
    index5_unique = unique(sequence)
    colors = [findfirst(x -> x == seq, index5_unique) for seq in sequence]

    # Graphique 3D principal
    p1 = scatter3d(pc1_theo, pc1_obs, pc1_diff,
        color=colors,
        title="Signature Évolutive 3D",
        xlabel="PC1 Théorique",
        ylabel="PC1 Observé",
        zlabel="Volatilité Moyenne",
        markersize=4,
        alpha=0.7
    )

    # Ajouter la trajectoire temporelle
    plot3d!(pc1_theo, pc1_obs, pc1_diff,
        color=:black,
        linewidth=2,
        alpha=0.5,
        label="Trajectoire"
    )

    # Projections 2D
    p2 = scatter(pc1_theo, pc1_obs,
        color=colors,
        title="Projection Théorique-Observé",
        xlabel="PC1 Théorique",
        ylabel="PC1 Observé",
        alpha=0.7
    )
    plot!(pc1_theo, pc1_obs, color=:black, linewidth=1, alpha=0.5)

    p3 = scatter(pc1_theo, pc1_diff,
        color=colors,
        title="Projection Théorique-Volatilité",
        xlabel="PC1 Théorique",
        ylabel="Volatilité",
        alpha=0.7
    )
    plot!(pc1_theo, pc1_diff, color=:black, linewidth=1, alpha=0.5)

    p4 = scatter(pc1_obs, pc1_diff,
        color=colors,
        title="Projection Observé-Volatilité",
        xlabel="PC1 Observé",
        ylabel="Volatilité",
        alpha=0.7
    )
    plot!(pc1_obs, pc1_diff, color=:black, linewidth=1, alpha=0.5)

    # Combiner
    p_combined = plot(p1, p2, p3, p4,
        layout=(2, 2),
        size=(1600, 1200),
        dpi=graphique.dpi,
        plot_title="🌌 SIGNATURE ÉVOLUTIVE 3D\nPartie: $(graphique.partie_name)"
    )

    # Sauvegarder
    savefig(p_combined, joinpath(graphique.output_dir, "09_signature_evolutive_3d.png"))
    println("✅ Signature évolutive 3D sauvegardée")
end

"""
    generer_mega_dashboard(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T

Génère le méga-dashboard interactif complet.
Vue d'ensemble de toutes les analyses.
"""
function generer_mega_dashboard(graphique::GraphiqueEntropique{T}, metriques::Dict, sequence::Vector{String}) where T
    println("📊 Génération du méga-dashboard complet...")

    principales = metriques["principales"]
    differentiels = metriques["differentiels"]
    n_mains = metriques["n_mains"]

    # 1. Résumé des métriques clés
    shannon_o = principales[13, :]
    info_mut_o = principales[19, :]
    cond_o = principales[17, :]

    p1 = plot(title="Métriques Clés")
    plot!(1:n_mains, shannon_o, label="ShannonO", linewidth=2, color=:blue)
    plot!(1:n_mains, info_mut_o, label="InfoMutO", linewidth=2, color=:red)
    plot!(1:n_mains, cond_o, label="CondO", linewidth=2, color=:green)
    xlabel!("Main")
    ylabel!("Valeur")

    # 2. Distribution INDEX5
    index5_counts = Dict{String, Int}()
    for idx5 in sequence
        index5_counts[idx5] = get(index5_counts, idx5, 0) + 1
    end

    p2 = bar(collect(keys(index5_counts)), collect(values(index5_counts)),
        title="Distribution INDEX5",
        xlabel="INDEX5",
        ylabel="Fréquence",
        color=:lightblue,
        xrotation=45
    )

    # 3. Volatilité globale
    volatilite_globale = vec(sqrt.(sum(differentiels.^2, dims=1)))

    p3 = plot(2:n_mains, volatilite_globale,
        title="Volatilité Globale",
        xlabel="Main",
        ylabel="Volatilité",
        linewidth=2,
        color=:purple
    )

    # 4. Corrélations moyennes
    corr_theo_obs = []
    for i in 1:12
        push!(corr_theo_obs, cor(principales[i, :], principales[i+12, :]))
    end

    p4 = bar(1:12, abs.(corr_theo_obs),
        title="Corrélations |Théo-Obs|",
        xlabel="Métrique",
        ylabel="|Corrélation|",
        color=:orange
    )

    # 5. Évolution de la complexité
    complexite = shannon_o .+ info_mut_o

    p5 = plot(1:n_mains, complexite,
        title="Complexité Composite",
        xlabel="Main",
        ylabel="Shannon + InfoMut",
        linewidth=2,
        color=:darkgreen
    )

    # 6. Score de prédictibilité final
    predictibilite = (1.0 .- cond_o) .* info_mut_o ./ (shannon_o .+ 1e-10)

    p6 = plot(1:n_mains, predictibilite,
        title="Score de Prédictibilité",
        xlabel="Main",
        ylabel="Score",
        linewidth=3,
        color=:red
    )

    # Combiner en méga-dashboard
    p_mega = plot(p1, p2, p3, p4, p5, p6,
        layout=(3, 2),
        size=(1800, 1400),
        dpi=graphique.dpi,
        plot_title="🎛️ MÉGA-DASHBOARD COMPLET\nPartie: $(graphique.partie_name)"
    )

    # Sauvegarder
    savefig(p_mega, joinpath(graphique.output_dir, "10_mega_dashboard_complet.png"))
    println("✅ Méga-dashboard complet sauvegardé")

    # Créer un fichier de résumé
    resume_path = joinpath(graphique.output_dir, "RESUME_ANALYSE.txt")
    open(resume_path, "w") do f
        write(f, "🎯 RÉSUMÉ DE L'ANALYSE ENTROPIQUE\n")
        write(f, "================================\n\n")
        write(f, "Partie: $(graphique.partie_name)\n")
        write(f, "Nombre de mains: $n_mains\n")
        write(f, "INDEX5 uniques: $(length(unique(sequence)))\n\n")

        write(f, "📊 MÉTRIQUES FINALES:\n")
        write(f, "- Shannon Observé final: $(round(shannon_o[end], digits=4))\n")
        write(f, "- Information Mutuelle finale: $(round(info_mut_o[end], digits=4))\n")
        write(f, "- Entropie Conditionnelle finale: $(round(cond_o[end], digits=4))\n")
        write(f, "- Score de Prédictibilité final: $(round(predictibilite[end], digits=4))\n\n")

        write(f, "🎨 GRAPHIQUES GÉNÉRÉS:\n")
        write(f, "1. Heatmap maîtresse 48x60\n")
        write(f, "2. Analyse PCA 3D\n")
        write(f, "3. Matrice de corrélation 48x48\n")
        write(f, "4. Graphique temporel multi-panneaux\n")
        write(f, "5. Radar géant 48-axes\n")
        write(f, "6. Graphique de prédiction INDEX5\n")
        write(f, "7. Graphique de flux entropique\n")
        write(f, "8. Dashboard de détection de régimes\n")
        write(f, "9. Signature évolutive 3D\n")
        write(f, "10. Méga-dashboard complet\n")
    end

    println("📄 Résumé d'analyse créé: $resume_path")
end

"""
    generer_graphiques_partie_interactive(analyzer::EntropyAnalyzer, filepath::String)

Interface interactive pour générer tous les graphiques d'une partie.
Crée automatiquement un dossier avec tous les graphiques révélateurs.
"""
function generer_graphiques_partie_interactive(analyzer::EntropyAnalyzer, filepath::String)
    println("\n🎨 GÉNÉRATION DE GRAPHIQUES COMPLETS")
    println("=" ^ 50)

    # Vérifier la disponibilité des packages de graphiques
    if !verifier_packages_graphiques()
        println("❌ PACKAGES DE VISUALISATION MANQUANTS!")
        println("📦 Pour utiliser cette fonctionnalité, installez les packages suivants:")
        println("   julia> import Pkg")
        println("   julia> Pkg.add([\"Plots\", \"StatsPlots\", \"PlotlyJS\", \"MultivariateStats\"])")
        println("\n💡 Une fois installés, relancez le programme.")
        return
    end

    try
        # Utiliser le bon fichier de données
        data_filepath = "C:\\Users\\<USER>\\Desktop\\17\\partie\\dataset_baccarat_lupasco_20250704_092825_condensed.json"

        # Charger les données
        println("📁 Chargement des données depuis: $data_filepath")
        data = JSON.parsefile(data_filepath)
        parties = data["parties_condensees"]

        println("📊 $(length(parties)) parties disponibles")
        print("Numéro de la partie à analyser (1-$(length(parties))): ")

        partie_num_str = readline()
        partie_num = parse(Int, partie_num_str)

        if partie_num < 1 || partie_num > length(parties)
            println("❌ Numéro de partie invalide!")
            return
        end

        # Extraire la séquence
        partie_data = parties[partie_num]
        partie_name = "Partie_$(partie_data["partie_number"])"

        println("\n🔄 Extraction de la séquence...")
        sequence = String[]

        for main in partie_data["mains_condensees"]
            # Ignorer les mains avec des valeurs vides
            if main["index5"] != "" && main["index5"] !== nothing
                push!(sequence, main["index5"])
            end
        end

        println("🔍 Séquence extraite: $(length(sequence)) mains valides")

        if length(sequence) < 10
            println("❌ Séquence trop courte pour une analyse graphique complète!")
            return
        end

        # Créer le générateur de graphiques
        println("\n🎨 Initialisation du générateur de graphiques...")
        graphique = GraphiqueEntropique(partie_name)

        println("📁 Dossier de sortie créé: $(graphique.output_dir)")

        # Générer tous les graphiques
        println("\n🚀 Génération de tous les graphiques...")
        println("⏳ Cela peut prendre quelques minutes...")

        generer_tous_graphiques(graphique, sequence)

        println("\n🎉 GÉNÉRATION TERMINÉE!")
        println("=" ^ 50)
        println("📁 Tous les graphiques ont été sauvegardés dans: $(graphique.output_dir)")
        println("📊 10 graphiques révélateurs générés:")
        println("   1. 🧠 Heatmap maîtresse 48x60")
        println("   2. 🎯 Analyse PCA 3D")
        println("   3. 🔗 Matrice de corrélation 48x48")
        println("   4. 📈 Graphique temporel multi-panneaux")
        println("   5. 🎯 Radar géant 48-axes")
        println("   6. 🎯 Graphique de prédiction INDEX5")
        println("   7. 🌊 Graphique de flux entropique")
        println("   8. 🚨 Dashboard de détection de régimes")
        println("   9. 🌌 Signature évolutive 3D")
        println("   10. 🎛️ Méga-dashboard complet")
        println("📄 + Résumé d'analyse détaillé")

        println("\n💡 Conseil: Ouvrez le dossier $(graphique.output_dir) pour explorer tous les graphiques!")

    catch e
        println("❌ Erreur lors de la génération des graphiques: $e")
        if isa(e, SystemError)
            println("💡 Vérifiez que les packages de visualisation sont installés:")
            println("   - Plots.jl")
            println("   - StatsPlots.jl")
            println("   - PlotlyJS.jl")
            println("   - MultivariateStats.jl")
        end
    end
end

# ─────────────────────────────────────────────────────────────────────────────
# ANCIEN TABLEAU DÉSACTIVÉ
# ─────────────────────────────────────────────────────────────────────────────
#
# ANCIEN FORMAT DE TABLEAU (DÉSACTIVÉ) :
# Main       DiffEG    DiffSEG    DiffCEG    INDEX5_Observé         Mt5     DiffT5    DiffDivEG      DiffC
#
# ANCIEN CODE D'AFFICHAGE (DÉSACTIVÉ) :
# println(@sprintf("%-6s %10s %10s %10s    %-15s %10s %10s %12s %10s",
#         "Main", "DiffEG", "DiffSEG", "DiffCEG", "INDEX5_Observé", "Mt5", "DiffT5", "DiffDivEG", "DiffC"))
# println("-"^119)
#
# @printf("%-6d %10.4f %10.4f %10.4f    %-15s %10.4f %10.4f %12.4f %10.4f\n",
#        i,                                        # Main
#        diff.diff_entrop_g,                       # DiffEG (différentiel)
#        diff.diff_seg,                            # DiffSEG (différentiel)
#        diff.diff_ceg,                            # DiffCEG (différentiel)
#        observed_index5,                          # INDEX5_Observé
#        metrics.metric_entropy,                   # Mt5
#        diff.diff_taux,                           # DiffT5
#        diff.diff_div_entrop_g,                   # DiffDivEG (différentiel)
#        diff.diff_cond)                           # DiffC
#
# ─────────────────────────────────────────────────────────────────────────────

# ═══════════════════════════════════════════════════════════════════════════════
# POINT D'ENTRÉE
# ═══════════════════════════════════════════════════════════════════════════════

# Exécuter le programme principal si ce fichier est exécuté directement
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end
