"""
ANALYSEUR D'ENTROPIE BACCARAT - INDEX5 (Julia Version)
=====================================================

Programme d'analyse de l'évolution de l'entropie pour l'INDEX5 au cours d'une partie de baccarat.
Basé sur les formules d'entropie de Shannon et implémenté selon les bonnes pratiques Julia.

Architecture modulaire avec types paramétriques et dispatch multiple.
"""

using JSON
using Statistics
using LinearAlgebra
using Printf

# ═══════════════════════════════════════════════════════════════════════════════
# TYPES ET STRUCTURES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    EntropyAnalyzer{T<:AbstractFloat}

Analyseur d'entropie pour le baccarat basé sur les formules de Shannon.

# Fields
- `base::T`: Base pour les calculs logarithmiques (défaut: 2.0 pour bits)
- `epsilon::T`: Petite valeur pour éviter log(0) (défaut: 1e-12)
- `theoretical_probs::Dict{String,T}`: Probabilités théoriques INDEX5

# Examples
```julia
analyzer = EntropyAnalyzer{Float64}()
entropy = calculate_shannon_entropy(analyzer, [0.5, 0.5])
```
"""
struct EntropyAnalyzer{T<:AbstractFloat}
    base::T
    epsilon::T
    theoretical_probs::Dict{String,T}
    
    function EntropyAnalyzer{T}(
        base::T = T(2.0),
        epsilon::T = T(1e-12)
    ) where T<:AbstractFloat
        if base <= 1
            throw(ArgumentError("Base must be > 1, got $base"))
        end
        if epsilon <= 0
            throw(ArgumentError("Epsilon must be > 0, got $epsilon"))
        end
        
        # Probabilités théoriques INDEX5 normalisées
        theoretical_probs = Dict{String,T}(
            "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
            "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
            "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
            "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
            "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
            "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
            "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
            "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
            "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
        )
        
        new{T}(base, epsilon, theoretical_probs)
    end
end

# Constructeur de convenance
EntropyAnalyzer(args...) = EntropyAnalyzer{Float64}(args...)

"""
    EntropyMetrics{T<:AbstractFloat}

Structure pour stocker les métriques d'entropie calculées.
"""
struct EntropyMetrics{T<:AbstractFloat}
    position::Int
    sequence_length::Int
    unique_values::Int
    simple_entropy::T
    simple_entropy_theoretical::T
    conditional_entropy::T
    metric_entropy::T
    entropy_rate::T
    entropy_aep_observed::T        # EGobs (Entropie Générale Observée)
    conf_eg::T                     # ConfEG = |EntropG - EGobs|
    struct_eg::T                   # StructEG = EntropG/EGobs
    block_entropies::Vector{T}
end

"""
    PredictiveDifferentials{T<:AbstractFloat}

Structure pour les différentiels prédictifs selon les définitions exactes.
Correspondances:
- DiffC = DiffCond (Différentiel Entropie Conditionnelle)
- DiffT5 = DiffT5 (Différentiel T5)
- DiffDivEG = DiffDivEntropG (Différentiel Diversité Entropique)
- DiffEG = DiffEG (Différentiel Entropie Générale)
- DiffEGobs = DiffEGobs (Différentiel Entropie Générale Observée)
- DiffSEG = DiffSEG (Différentiel Structure Entropique)
- DiffCEG = DiffCEG (Différentiel Conformité Entropique)
- SCORE = (DiffC + DiffEG) / (DiffT5 + DiffDivEG)
"""
struct PredictiveDifferentials{T<:AbstractFloat}
    diff_cond::T          # DiffC = DiffCond
    diff_taux::T          # DiffT5 = DiffT5
    diff_div_entrop_g::T  # DiffDivEG = DiffDivEntropG
    diff_entrop_g::T      # DiffEG = DiffEG
    diff_egobs::T         # DiffEGobs = DiffEGobs
    diff_seg::T           # DiffSEG = DiffSEG
    diff_ceg::T           # DiffCEG = DiffCEG
    score::T              # SCORE = (DiffC + DiffEG) / (DiffT5 + DiffDivEG)
end

"""
    FormulasObserved{T<:AbstractFloat}

Classe contenant les 12 formules OBS (observées) pour fenêtres croissantes.
Utilise les fréquences réelles observées dans la séquence de jeu.

Basé sur le fichier Formules1.txt - 12 formules × version observée.
Chaque formule calcule une métrique d'entropie pour la main n en utilisant
les patterns réellement observés dans la séquence jusqu'à cette main.

# Fields
- `base::T`: Base pour les calculs logarithmiques (défaut: 2.0 pour bits)
- `epsilon::T`: Petite valeur pour éviter log(0) (défaut: 1e-12)

# Formules implémentées (version observée)
1. Shannon Jointe : H_obs(X₁, X₂, ..., Xₙ)
2. AEP : H_AEP_obs(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_obs(xᵢ)
3. Taux d'Entropie : H_obs(Ξ) = lim_{n→∞} (1/n) H_obs(X₁, ..., Xₙ)
4. Entropie Métrique : h_μ_obs(T) = sup{h_μ_obs(T, α)}
5. Conditionnelle Cumulative : H_obs(Xₙ|X₁, ..., Xₙ₋₁)
6. Divergence KL : D_KL_obs_theo(P_n||Q_n)
7. Information Mutuelle : I_obs(X₁ⁿ; Y₁ⁿ)
8. Entropie Croisée : H_cross_obs_theo(P_n, Q_n)
9. Entropie Topologique : h_top_obs(f)
10. Block Cumulative : H_n_obs = H_obs(X₁, ..., Xₙ)
11. Conditionnelle Décroissante : u_n_obs = H_obs(Xₙ|X₁, ..., Xₙ₋₁)
12. Théorème AEP : lim_{n→∞} -(1/n) log₂ p_obs(X₁ⁿ) = H_obs(Ξ)
"""
struct FormulasObserved{T<:AbstractFloat}
    base::T
    epsilon::T

    function FormulasObserved{T}(
        base::T = T(2.0),
        epsilon::T = T(1e-12)
    ) where T<:AbstractFloat
        if base <= 1
            throw(ArgumentError("Base must be > 1, got $base"))
        end
        if epsilon <= 0
            throw(ArgumentError("Epsilon must be > 0, got $epsilon"))
        end

        new{T}(base, epsilon)
    end
end

# Constructeur de convenance
FormulasObserved(args...) = FormulasObserved{Float64}(args...)

"""
    FormulasTheoretical{T<:AbstractFloat}

Classe contenant les 12 formules THEO (théoriques) pour fenêtres croissantes.
Utilise les probabilités théoriques INDEX5 du modèle de référence.

Basé sur le fichier Formules1.txt - 12 formules × version théorique.
Chaque formule calcule une métrique d'entropie pour la main n en utilisant
les probabilités théoriques INDEX5 définies dans le modèle.

# Fields
- `base::T`: Base pour les calculs logarithmiques (défaut: 2.0 pour bits)
- `epsilon::T`: Petite valeur pour éviter log(0) (défaut: 1e-12)
- `theoretical_probs::Dict{String,T}`: Probabilités théoriques INDEX5

# Formules implémentées (version théorique)
1. Shannon Jointe : H_theo(X₁, X₂, ..., Xₙ)
2. AEP : H_AEP_theo(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_theo(xᵢ)
3. Taux d'Entropie : H_theo(Ξ) = lim_{n→∞} (1/n) H_theo(X₁, ..., Xₙ)
4. Entropie Métrique : h_μ_theo(T) = sup{h_μ_theo(T, α)}
5. Conditionnelle Cumulative : H_theo(Xₙ|X₁, ..., Xₙ₋₁)
6. Divergence KL : D_KL_theo_unif(P_n||U_n)
7. Information Mutuelle : I_theo(X₁ⁿ; Y₁ⁿ)
8. Entropie Croisée : H_cross_theo_unif(P_n, U_n)
9. Entropie Topologique : h_top_theo(f)
10. Block Cumulative : H_n_theo = H_theo(X₁, ..., Xₙ)
11. Conditionnelle Décroissante : u_n_theo = H_theo(Xₙ|X₁, ..., Xₙ₋₁)
12. Théorème AEP : lim_{n→∞} -(1/n) log₂ p_theo(X₁ⁿ) = H_theo(Ξ)
"""
struct FormulasTheoretical{T<:AbstractFloat}
    base::T
    epsilon::T
    theoretical_probs::Dict{String,T}

    function FormulasTheoretical{T}(
        base::T = T(2.0),
        epsilon::T = T(1e-12)
    ) where T<:AbstractFloat
        if base <= 1
            throw(ArgumentError("Base must be > 1, got $base"))
        end
        if epsilon <= 0
            throw(ArgumentError("Epsilon must be > 0, got $epsilon"))
        end

        # Probabilités théoriques INDEX5 normalisées (identiques à EntropyAnalyzer)
        theoretical_probs = Dict{String,T}(
            "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
            "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
            "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
            "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
            "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
            "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
            "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
            "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
            "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
        )

        new{T}(base, epsilon, theoretical_probs)
    end
end

# Constructeur de convenance
FormulasTheoretical(args...) = FormulasTheoretical{Float64}(args...)

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS UTILITAIRES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    safe_log(x::T, base::T, epsilon::T) where T<:AbstractFloat -> T

Calcul sécurisé du logarithme avec gestion de log(0).
"""
function safe_log(x::T, base::T, epsilon::T) where T<:AbstractFloat
    if x <= zero(T)
        x = epsilon
    end
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return log(x) / log(base)
    return zero(T)  # Valeur par défaut temporaire
end

"""
    validate_probabilities(probs::Vector{T}) where T<:AbstractFloat -> Vector{T}

Valide et normalise un vecteur de probabilités.
"""
function validate_probabilities(probs::Vector{T}) where T<:AbstractFloat
    if isempty(probs)
        throw(ArgumentError("Probability vector cannot be empty"))
    end

    # Convertir en Float64 si nécessaire et filtrer les valeurs négatives
    clean_probs = max.(probs, zero(T))

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # Normaliser
    # total = sum(clean_probs)
    # if total ≈ zero(T)
    #     throw(ArgumentError("All probabilities are zero"))
    # end
    # return clean_probs ./ total

    # Valeur par défaut temporaire
    return clean_probs
end

# ═══════════════════════════════════════════════════════════════════════════════
# IMPLÉMENTATION DES FORMULES OBSERVÉES (FormulasObserved)
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule1A_shannon_jointe_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 1A : Entropie de Shannon Jointe (VERSION OBSERVÉE)
Formule : H_obs(X₁, X₂, ..., Xₙ) = -∑ p_obs(x₁,...,xₙ) log₂ p_obs(x₁,...,xₙ)

Calcule l'entropie jointe observée pour la fenêtre croissante de la main 1 à la main n.
Utilise les fréquences réellement observées dans la séquence.
"""
function calculer_formule1A_shannon_jointe_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences de chaque combinaison
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie de Shannon observée
    entropy = zero(T)
    total = length(subsequence)

    for count in values(counts)
        if count > 0
            p_obs = T(count) / T(total)
            entropy -= p_obs * (log(p_obs) / log(formulas.base))
        end
    end

    return entropy
end

"""
    calculer_formule2A_aep_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 2A : Entropie par Symbole AEP (VERSION OBSERVÉE)
Formule : H_AEP_obs(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_obs(xᵢ)

Calcule l'entropie moyenne par symbole selon l'AEP avec probabilités observées.
"""
function calculer_formule2A_aep_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences pour calculer p_obs
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer la somme des logarithmes avec probabilités observées
    # FORMULE CORRIGÉE : H_AEP_obs(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_obs(xᵢ)
    total_log_prob = zero(T)
    for value in subsequence
        p_obs = T(counts[value]) / T(n)
        if p_obs > zero(T)
            # log(p_obs) est négatif, donc on soustrait pour obtenir une valeur positive
            total_log_prob -= log(p_obs) / log(formulas.base)
        else
            # Gestion du cas p_obs = 0 avec epsilon
            total_log_prob -= log(formulas.epsilon) / log(formulas.base)
        end
    end

    # Appliquer la formule AEP observée : moyenne des -log₂(p_obs)
    return total_log_prob / n
end

"""
    calculer_formule3A_taux_entropie_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 3A : Taux d'Entropie (VERSION OBSERVÉE)
Formule : H_obs(Ξ) = lim_{n→∞} (1/n) H_obs(X₁, X₂, ..., Xₙ)

Estime le taux d'entropie observé comme limite asymptotique.
"""
function calculer_formule3A_taux_entropie_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Calculer l'entropie jointe observée
    h_joint = calculer_formule1A_shannon_jointe_obs(formulas, sequence, n)

    # Estimer le taux d'entropie : (1/n) × H_obs(X₁, ..., Xₙ)
    return h_joint / n
end

"""
    calculer_formule4A_entropie_metrique_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 4A : Entropie Métrique Kolmogorov-Sinai (VERSION OBSERVÉE)
Formule : h_μ_obs(T) = sup{h_μ_obs(T, α) : α partition finie}

Approximation pratique de l'entropie métrique observée.
"""
function calculer_formule4A_entropie_metrique_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Approximation : h_metric ≈ H(n)/n pour la partition naturelle INDEX5
    h_joint = calculer_formule1A_shannon_jointe_obs(formulas, sequence, n)
    return h_joint / n
end

"""
    calculer_formule5A_conditionnelle_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 5A : Entropie Conditionnelle Cumulative (VERSION OBSERVÉE)
Formule : H_obs(Xₙ|X₁, ..., Xₙ₋₁) = H_obs(X₁, ..., Xₙ) - H_obs(X₁, ..., Xₙ₋₁)

Calcule l'information observée apportée par le n-ème symbole.
"""
function calculer_formule5A_conditionnelle_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 1 || n > length(sequence)
        return zero(T)
    end

    # H_obs(X₁, ..., Xₙ)
    h_n = calculer_formule1A_shannon_jointe_obs(formulas, sequence, n)

    # H_obs(X₁, ..., Xₙ₋₁)
    h_n_minus_1 = calculer_formule1A_shannon_jointe_obs(formulas, sequence, n-1)

    # Entropie conditionnelle
    return h_n - h_n_minus_1
end

"""
    calculer_formule6A_divergence_kl_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 6A : Entropie Relative - Divergence KL (VERSION OBSERVÉE vs THÉORIQUE)
Formule : D_KL_obs_theo(P_n||Q_n) = ∑ᵢ₌₁ⁿ p_obs(xᵢ) log₂(p_obs(xᵢ)/p_theo(xᵢ))

CORRECTION SELON Formules1.txt :
- Compare distribution OBSERVÉE vs THÉORIQUE INDEX5 (pas vs uniforme)
- Utilise les probabilités INDEX5 comme référence théorique
"""
function calculer_formule6A_divergence_kl_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # CORRECTION : Utiliser les probabilités INDEX5 théoriques (pas uniforme)
    # Probabilités théoriques INDEX5 (identiques à FormulasTheoretical)
    theoretical_probs = Dict{String,T}(
        "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
        "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
        "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
        "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
        "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
        "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
        "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
        "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
        "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
    )

    # Calculer la divergence KL observée vs théorique INDEX5
    divergence = zero(T)
    total = length(subsequence)

    for (value, count) in counts
        p_obs = T(count) / T(total)
        if p_obs > zero(T)
            p_theo = get(theoretical_probs, value, formulas.epsilon)
            if p_theo > zero(T)
                divergence += p_obs * (log(p_obs/p_theo) / log(formulas.base))
            end
        end
    end

    return divergence
end

"""
    calculer_formule7A_information_mutuelle_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 7A : Information Mutuelle Cumulative (VERSION OBSERVÉE)
Formule : I_obs(X₁ⁿ; Y₁ⁿ) = H_obs(X₁ⁿ) + H_obs(Y₁ⁿ) - H_obs(X₁ⁿ, Y₁ⁿ)

CORRECTION SELON Formules1.txt :
- Dépendance observée entre patterns temporels
- Approximation : Auto-corrélation entre positions paires et impaires
"""
function calculer_formule7A_information_mutuelle_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 2 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # CORRECTION : Séparer en positions paires et impaires pour mesurer l'auto-corrélation
    seq_paires = String[]
    seq_impaires = String[]

    for i in 1:length(subsequence)
        if i % 2 == 0
            push!(seq_paires, subsequence[i])
        else
            push!(seq_impaires, subsequence[i])
        end
    end

    # Si une des séquences est vide, retourner 0
    if isempty(seq_paires) || isempty(seq_impaires)
        return zero(T)
    end

    # H_obs(positions_paires)
    h_paires = calculer_formule1A_shannon_jointe_obs(formulas, seq_paires, length(seq_paires))

    # H_obs(positions_impaires)
    h_impaires = calculer_formule1A_shannon_jointe_obs(formulas, seq_impaires, length(seq_impaires))

    # H_obs(total) - entropie jointe de toute la séquence
    h_total = calculer_formule1A_shannon_jointe_obs(formulas, subsequence, length(subsequence))

    # Information mutuelle : I(X;Y) = H(X) + H(Y) - H(X,Y)
    # Approximation avec auto-corrélation temporelle
    return h_paires + h_impaires - h_total
end

"""
    calculer_formule8A_entropie_croisee_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 8A : Entropie Croisée Cumulative (VERSION OBSERVÉE vs THÉORIQUE)
Formule : H_cross_obs_theo(P_n, Q_n) = -∑ᵢ₌₁ⁿ p_obs(xᵢ) log₂ p_theo(xᵢ)

CORRECTION SELON Formules1.txt :
- Coût d'encodage des données observées avec le modèle INDEX5
- Utilise les probabilités INDEX5 comme distribution de référence
"""
function calculer_formule8A_entropie_croisee_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # CORRECTION : Utiliser les probabilités INDEX5 théoriques
    theoretical_probs = Dict{String,T}(
        "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
        "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
        "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
        "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
        "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
        "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
        "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
        "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
        "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
    )

    # Calculer l'entropie croisée observée vs théorique INDEX5
    cross_entropy = zero(T)
    total = length(subsequence)

    for (value, count) in counts
        p_obs = T(count) / T(total)
        if p_obs > zero(T)
            p_theo = get(theoretical_probs, value, formulas.epsilon)
            if p_theo > zero(T)
                cross_entropy -= p_obs * (log(p_theo) / log(formulas.base))
            else
                cross_entropy -= p_obs * (log(formulas.epsilon) / log(formulas.base))
            end
        end
    end

    return cross_entropy
end

"""
    calculer_formule9A_entropie_topologique_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 9A : Entropie Topologique Cumulative (VERSION OBSERVÉE)
Formule : h_top_obs(f) = lim_{n→∞} (1/n) log₂ |{orbites périodiques de période ≤ n}|

Approximation pratique basée sur la diversité des patterns observés.
"""
function calculer_formule9A_entropie_topologique_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter le nombre de patterns distincts (approximation de la complexité topologique)
    unique_patterns = Set(subsequence)
    num_patterns = length(unique_patterns)

    # Approximation : h_top ≈ (1/n) log₂(nombre_patterns_distincts)
    if num_patterns > 0
        return (log(T(num_patterns)) / log(formulas.base)) / n
    else
        return zero(T)
    end
end

"""
    calculer_formule10A_block_cumulative_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 10A : Entropie de Block Cumulative (VERSION OBSERVÉE)
Formule : H_n_obs = H_obs(X₁, X₂, ..., Xₙ)

Identique à l'entropie jointe de Shannon - incluse pour cohérence avec Formules1.txt.
"""
function calculer_formule10A_block_cumulative_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    # Cette formule est identique à la formule 1A (Shannon Jointe)
    return calculer_formule1A_shannon_jointe_obs(formulas, sequence, n)
end

"""
    calculer_formule11A_conditionnelle_decroissante_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 11A : Entropie Conditionnelle Décroissante (VERSION OBSERVÉE)
Formule : u_n_obs = H_obs(Xₙ|X₁, X₂, ..., Xₙ₋₁)

Identique à la formule 5A (Conditionnelle Cumulative) - incluse pour cohérence.
"""
function calculer_formule11A_conditionnelle_decroissante_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    # Cette formule est identique à la formule 5A (Conditionnelle Cumulative)
    return calculer_formule5A_conditionnelle_obs(formulas, sequence, n)
end

"""
    calculer_formule12A_theoreme_aep_obs(formulas::FormulasObserved{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 12A : Théorème AEP Shannon-McMillan-Breiman (VERSION OBSERVÉE)
Formule : lim_{n→∞} -(1/n) log₂ p_obs(X₁ⁿ) = H_obs(Ξ)

Convergence vers le taux d'entropie observé selon le théorème AEP.
"""
function calculer_formule12A_theoreme_aep_obs(
    formulas::FormulasObserved{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Calculer -(1/n) log₂ p_obs(X₁ⁿ) selon le théorème AEP
    # Ceci est équivalent à la formule 2A (AEP observée)
    return calculer_formule2A_aep_obs(formulas, sequence, n)
end

# ═══════════════════════════════════════════════════════════════════════════════
# IMPLÉMENTATION DES FORMULES THÉORIQUES (FormulasTheoretical)
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule1B_shannon_jointe_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 1B : Entropie de Shannon Jointe (VERSION THÉORIQUE)
Formule : H_theo(X₁, X₂, ..., Xₙ) = -∑ p_theo(x₁,...,xₙ) log₂ p_theo(x₁,...,xₙ)

Calcule l'entropie jointe théorique pour la fenêtre croissante de la main 1 à la main n.
Utilise les probabilités théoriques INDEX5 du modèle.
"""
function calculer_formule1B_shannon_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # CORRECTION : Calculer l'entropie de Shannon de la DISTRIBUTION théorique
    # Compter les occurrences observées dans la séquence
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie de Shannon théorique basée sur les probabilités INDEX5
    entropy = zero(T)
    total = length(subsequence)

    # Pour chaque valeur unique observée, utiliser sa probabilité théorique INDEX5
    for (value, count) in counts
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            # Entropie de Shannon : -∑ p_theo(x) log₂ p_theo(x)
            # Pondérée par la fréquence d'apparition dans la séquence
            weight = T(count) / T(total)
            entropy -= weight * p_theo * (log(p_theo) / log(formulas.base))
        end
    end

    return entropy
end

"""
    calculer_formule2B_aep_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 2B : Entropie par Symbole AEP (VERSION THÉORIQUE)
Formule : H_AEP_theo(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_theo(xᵢ)

Calcule l'entropie moyenne par symbole selon l'AEP avec probabilités théoriques INDEX5.
"""
function calculer_formule2B_aep_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Calculer la somme des logarithmes avec probabilités théoriques
    # FORMULE CORRIGÉE : H_AEP_theo(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_theo(xᵢ)
    total_log_prob = zero(T)
    for value in subsequence
        p_theo = get(formulas.theoretical_probs, value, zero(T))
        if p_theo > zero(T)
            # log(p_theo) est négatif, donc on soustrait pour obtenir une valeur positive
            total_log_prob -= log(p_theo) / log(formulas.base)
        else
            # Gestion du cas p_theo = 0 avec epsilon
            total_log_prob -= log(formulas.epsilon) / log(formulas.base)
        end
    end

    # Appliquer la formule AEP théorique : moyenne des -log₂(p_theo)
    return total_log_prob / n
end

"""
    calculer_formule3B_taux_entropie_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 3B : Taux d'Entropie (VERSION THÉORIQUE)
Formule : H_theo(Ξ) = lim_{n→∞} (1/n) H_theo(X₁, X₂, ..., Xₙ)

Estime le taux d'entropie théorique comme limite asymptotique.
"""
function calculer_formule3B_taux_entropie_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Calculer l'entropie jointe théorique
    h_joint = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)

    # Estimer le taux d'entropie : (1/n) × H_theo(X₁, ..., Xₙ)
    return h_joint / n
end

"""
    calculer_formule4B_entropie_metrique_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 4B : Entropie Métrique Kolmogorov-Sinai (VERSION THÉORIQUE)
Formule : h_μ_theo(T) = sup{h_μ_theo(T, α) : α partition finie}

Approximation pratique de l'entropie métrique théorique.
"""
function calculer_formule4B_entropie_metrique_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Approximation : h_metric ≈ H(n)/n pour la partition naturelle INDEX5
    h_joint = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)
    return h_joint / n
end

"""
    calculer_formule5B_conditionnelle_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 5B : Entropie Conditionnelle Cumulative (VERSION THÉORIQUE)
Formule : H_theo(Xₙ|X₁, ..., Xₙ₋₁) = H_theo(X₁, ..., Xₙ) - H_theo(X₁, ..., Xₙ₋₁)

Calcule l'information théorique apportée par le n-ème symbole.
"""
function calculer_formule5B_conditionnelle_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 1 || n > length(sequence)
        return zero(T)
    end

    # H_theo(X₁, ..., Xₙ)
    h_n = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)

    # H_theo(X₁, ..., Xₙ₋₁)
    h_n_minus_1 = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n-1)

    # Entropie conditionnelle
    return h_n - h_n_minus_1
end

"""
    calculer_formule6B_divergence_kl_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 6B : Entropie Relative - Divergence KL (VERSION THÉORIQUE)
Formule : D_KL_theo_unif(P_n||U_n) = ∑ p_theo(x) log₂(p_theo(x)/p_unif(x))

Mesure la divergence entre distribution théorique INDEX5 et uniforme.
"""
function calculer_formule6B_divergence_kl_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Calculer la divergence KL théorique vs uniforme
    divergence = zero(T)
    processed_values = Set{String}()

    for value in subsequence
        if value ∉ processed_values
            push!(processed_values, value)

            p_theo = get(formulas.theoretical_probs, value, zero(T))
            p_unif = T(1.0/18.0)  # Distribution uniforme de référence

            if p_theo > zero(T) && p_unif > zero(T)
                divergence += p_theo * (log(p_theo/p_unif) / log(formulas.base))
            end
        end
    end

    return divergence
end

"""
    calculer_formule7B_information_mutuelle_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 7B : Information Mutuelle Cumulative (VERSION THÉORIQUE)
Formule : I_theo(X₁ⁿ; Y₁ⁿ) = H_theo(X₁ⁿ) + H_theo(Y₁ⁿ) - H_theo(X₁ⁿ, Y₁ⁿ)

CORRECTION SELON Formules1.txt :
- Dépendance théorique entre patterns temporels selon le modèle INDEX5
- Approximation : Auto-corrélation théorique entre positions paires et impaires
"""
function calculer_formule7B_information_mutuelle_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 2 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # CORRECTION : Calculer l'information mutuelle théorique basée sur les probabilités INDEX5
    # Séparer en positions paires et impaires pour mesurer l'auto-corrélation théorique
    values_paires = String[]
    values_impaires = String[]

    for i in 1:length(subsequence)
        if i % 2 == 0
            push!(values_paires, subsequence[i])
        else
            push!(values_impaires, subsequence[i])
        end
    end

    # Si une des séquences est vide, retourner 0
    if isempty(values_paires) || isempty(values_impaires)
        return zero(T)
    end

    # Calculer les entropies théoriques basées sur les probabilités INDEX5
    # H_theo(positions_paires) - entropie théorique des positions paires
    h_paires = zero(T)
    for value in values_paires
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            h_paires -= p_theo * (log(p_theo) / log(formulas.base))
        end
    end

    # H_theo(positions_impaires) - entropie théorique des positions impaires
    h_impaires = zero(T)
    for value in values_impaires
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            h_impaires -= p_theo * (log(p_theo) / log(formulas.base))
        end
    end

    # H_theo(total) - entropie théorique de toute la séquence
    h_total = zero(T)
    for value in subsequence
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            h_total -= p_theo * (log(p_theo) / log(formulas.base))
        end
    end

    # Information mutuelle théorique : I(X;Y) = H(X) + H(Y) - H(X,Y)
    return h_paires + h_impaires - h_total
end

"""
    calculer_formule8B_entropie_croisee_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 8B : Entropie Croisée Cumulative (VERSION THÉORIQUE)
Formule : H_cross_theo_unif(P_n, U_n) = -∑ p_theo(x) log₂ q_unif(x)

Mesure le coût d'encodage avec distribution uniforme vs théorique INDEX5.
"""
function calculer_formule8B_entropie_croisee_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Calculer l'entropie croisée théorique
    cross_entropy = zero(T)
    processed_values = Set{String}()

    for value in subsequence
        if value ∉ processed_values
            push!(processed_values, value)

            p_theo = get(formulas.theoretical_probs, value, zero(T))
            q_unif = T(1.0/18.0)  # Distribution uniforme de référence

            if p_theo > zero(T)
                if q_unif > zero(T)
                    cross_entropy -= p_theo * (log(q_unif) / log(formulas.base))
                else
                    cross_entropy -= p_theo * (log(formulas.epsilon) / log(formulas.base))
                end
            end
        end
    end

    return cross_entropy
end

"""
    calculer_formule9B_entropie_topologique_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 9B : Entropie Topologique Cumulative (VERSION THÉORIQUE)
Formule : h_top_theo(f) = lim_{n→∞} (1/n) log₂ |{orbites périodiques de période ≤ n}|

Approximation théorique basée sur la complexité du modèle INDEX5.
"""
function calculer_formule9B_entropie_topologique_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Approximation théorique : h_top ≈ (1/n) log₂(18) pour le modèle INDEX5
    # 18 = nombre total de valeurs possibles dans INDEX5
    log2_18 = log(T(18)) / log(formulas.base)
    return log2_18 / n
end

"""
    calculer_formule10B_block_cumulative_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 10B : Entropie de Block Cumulative (VERSION THÉORIQUE)
Formule : H_n_theo = H_theo(X₁, X₂, ..., Xₙ)

Identique à l'entropie jointe de Shannon théorique - incluse pour cohérence avec Formules1.txt.
"""
function calculer_formule10B_block_cumulative_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    # Cette formule est identique à la formule 1B (Shannon Jointe théorique)
    return calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)
end

"""
    calculer_formule11B_conditionnelle_decroissante_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 11B : Entropie Conditionnelle Décroissante (VERSION THÉORIQUE)
Formule : u_n_theo = H_theo(Xₙ|X₁, X₂, ..., Xₙ₋₁)

Identique à la formule 5B (Conditionnelle Cumulative théorique) - incluse pour cohérence.
"""
function calculer_formule11B_conditionnelle_decroissante_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    # Cette formule est identique à la formule 5B (Conditionnelle Cumulative théorique)
    return calculer_formule5B_conditionnelle_theo(formulas, sequence, n)
end

"""
    calculer_formule12B_theoreme_aep_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 12B : Théorème AEP Shannon-McMillan-Breiman (VERSION THÉORIQUE)
Formule : lim_{n→∞} -(1/n) log₂ p_theo(X₁ⁿ) = H_theo(Ξ)

Convergence vers le taux d'entropie théorique selon le théorème AEP.
"""
function calculer_formule12B_theoreme_aep_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Calculer -(1/n) log₂ p_theo(X₁ⁿ) selon le théorème AEP
    # Ceci est équivalent à la formule 2B (AEP théorique)
    return calculer_formule2B_aep_theo(formulas, sequence, n)
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS D'INTÉGRATION DES NOUVELLES CLASSES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_toutes_formules_pour_main_n(sequence::Vector{String}, n::Int) -> Dict{String, Float64}

Calcule toutes les 24 formules (12 OBS + 12 THEO) pour la main n.
Retourne un dictionnaire avec les résultats de chaque formule.

# Arguments
- `sequence::Vector{String}`: Séquence INDEX5 complète
- `n::Int`: Numéro de la main (fenêtre croissante de 1 à n)

# Returns
- `Dict{String, Float64}`: Dictionnaire contenant tous les résultats
"""
function calculer_toutes_formules_pour_main_n(sequence::Vector{String}, n::Int)
    # Initialiser les classes de formules
    formulas_obs = FormulasObserved{Float64}()
    formulas_theo = FormulasTheoretical{Float64}()

    # Dictionnaire pour stocker tous les résultats
    resultats = Dict{String, Float64}()

    # ─────────────────────────────────────────────────────────────────────────
    # CALCUL DES 12 FORMULES OBSERVÉES (OBS)
    # ─────────────────────────────────────────────────────────────────────────

    resultats["1A_Shannon_Jointe_OBS"] = calculer_formule1A_shannon_jointe_obs(formulas_obs, sequence, n)
    resultats["2A_AEP_OBS"] = calculer_formule2A_aep_obs(formulas_obs, sequence, n)
    resultats["3A_Taux_Entropie_OBS"] = calculer_formule3A_taux_entropie_obs(formulas_obs, sequence, n)
    resultats["4A_Entropie_Metrique_OBS"] = calculer_formule4A_entropie_metrique_obs(formulas_obs, sequence, n)
    resultats["5A_Conditionnelle_OBS"] = calculer_formule5A_conditionnelle_obs(formulas_obs, sequence, n)
    resultats["6A_Divergence_KL_OBS"] = calculer_formule6A_divergence_kl_obs(formulas_obs, sequence, n)
    resultats["7A_Information_Mutuelle_OBS"] = calculer_formule7A_information_mutuelle_obs(formulas_obs, sequence, n)
    resultats["8A_Entropie_Croisee_OBS"] = calculer_formule8A_entropie_croisee_obs(formulas_obs, sequence, n)
    resultats["9A_Entropie_Topologique_OBS"] = calculer_formule9A_entropie_topologique_obs(formulas_obs, sequence, n)
    resultats["10A_Block_Cumulative_OBS"] = calculer_formule10A_block_cumulative_obs(formulas_obs, sequence, n)
    resultats["11A_Conditionnelle_Decroissante_OBS"] = calculer_formule11A_conditionnelle_decroissante_obs(formulas_obs, sequence, n)
    resultats["12A_Theoreme_AEP_OBS"] = calculer_formule12A_theoreme_aep_obs(formulas_obs, sequence, n)

    # ─────────────────────────────────────────────────────────────────────────
    # CALCUL DES 12 FORMULES THÉORIQUES (THEO)
    # ─────────────────────────────────────────────────────────────────────────

    resultats["1B_Shannon_Jointe_THEO"] = calculer_formule1B_shannon_jointe_theo(formulas_theo, sequence, n)
    resultats["2B_AEP_THEO"] = calculer_formule2B_aep_theo(formulas_theo, sequence, n)
    resultats["3B_Taux_Entropie_THEO"] = calculer_formule3B_taux_entropie_theo(formulas_theo, sequence, n)
    resultats["4B_Entropie_Metrique_THEO"] = calculer_formule4B_entropie_metrique_theo(formulas_theo, sequence, n)
    resultats["5B_Conditionnelle_THEO"] = calculer_formule5B_conditionnelle_theo(formulas_theo, sequence, n)
    resultats["6B_Divergence_KL_THEO"] = calculer_formule6B_divergence_kl_theo(formulas_theo, sequence, n)
    resultats["7B_Information_Mutuelle_THEO"] = calculer_formule7B_information_mutuelle_theo(formulas_theo, sequence, n)
    resultats["8B_Entropie_Croisee_THEO"] = calculer_formule8B_entropie_croisee_theo(formulas_theo, sequence, n)
    resultats["9B_Entropie_Topologique_THEO"] = calculer_formule9B_entropie_topologique_theo(formulas_theo, sequence, n)
    resultats["10B_Block_Cumulative_THEO"] = calculer_formule10B_block_cumulative_theo(formulas_theo, sequence, n)
    resultats["11B_Conditionnelle_Decroissante_THEO"] = calculer_formule11B_conditionnelle_decroissante_theo(formulas_theo, sequence, n)
    resultats["12B_Theoreme_AEP_THEO"] = calculer_formule12B_theoreme_aep_theo(formulas_theo, sequence, n)

    return resultats
end

"""
    afficher_resultats_formules(resultats::Dict{String, Float64}, n::Int)

Affiche de manière formatée tous les résultats des 24 formules pour la main n.
"""
function afficher_resultats_formules(resultats::Dict{String, Float64}, n::Int)
    println("\n" * "="^80)
    println("RÉSULTATS DES 24 FORMULES POUR LA MAIN $n")
    println("="^80)

    println("\n📊 FORMULES OBSERVÉES (OBS) - Basées sur les fréquences réelles:")
    println("-"^60)
    obs_keys = sort([k for k in keys(resultats) if contains(k, "_OBS")])
    for key in obs_keys
        println("  $key: $(round(resultats[key], digits=6))")
    end

    println("\n🎯 FORMULES THÉORIQUES (THEO) - Basées sur le modèle INDEX5:")
    println("-"^60)
    theo_keys = sort([k for k in keys(resultats) if contains(k, "_THEO")])
    for key in theo_keys
        println("  $key: $(round(resultats[key], digits=6))")
    end

    println("\n" * "="^80)
end

"""
    tester_24_formules_interactive(default_filepath::String)

Interface interactive pour tester les 24 formules (12 OBS + 12 THEO) sur une séquence.
"""
function tester_24_formules_interactive(default_filepath::String)
    println("\n🧮 TEST DES 24 FORMULES (OBS + THEO)")
    println("="^50)

    # Charger les données
    print("Fichier JSON (Entrée = défaut '$default_filepath'): ")
    filepath_input = readline()
    filepath = isempty(filepath_input) ? default_filepath : filepath_input

    if !isfile(filepath)
        println("❌ Fichier non trouvé: $filepath")
        return
    end

    try
        # Charger et traiter les données
        println("📂 Chargement des données...")
        data = load_json_data(filepath)

        if isempty(data)
            println("❌ Aucune donnée trouvée dans le fichier")
            return
        end

        # Sélectionner une partie
        println("\n📊 Parties disponibles:")
        for (i, game) in enumerate(data[1:min(5, length(data))])
            sequence = get(game, "index5_sequence", String[])
            println("  $i. Partie avec $(length(sequence)) mains")
        end

        print("\nChoisir une partie (1-$(min(5, length(data)))): ")
        game_choice = readline()
        game_index = tryparse(Int, game_choice)

        if game_index === nothing || game_index < 1 || game_index > min(5, length(data))
            println("❌ Choix invalide")
            return
        end

        # Extraire la séquence
        selected_game = data[game_index]
        sequence = get(selected_game, "index5_sequence", String[])

        if isempty(sequence)
            println("❌ Séquence vide pour cette partie")
            return
        end

        println("\n✅ Séquence chargée: $(length(sequence)) mains")
        println("Premiers éléments: $(sequence[1:min(10, length(sequence))])")

        # Demander pour quelle main calculer
        print("\nPour quelle main calculer les formules (1-$(length(sequence))): ")
        main_input = readline()
        n = tryparse(Int, main_input)

        if n === nothing || n < 1 || n > length(sequence)
            println("❌ Numéro de main invalide")
            return
        end

        # Calculer toutes les formules
        println("\n🔄 Calcul des 24 formules en cours...")
        resultats = calculer_toutes_formules_pour_main_n(sequence, n)

        # Afficher les résultats
        afficher_resultats_formules(resultats, n)

        # Proposer de sauvegarder
        print("\n💾 Sauvegarder les résultats dans un fichier? (o/N): ")
        save_choice = lowercase(strip(readline()))

        if save_choice == "o" || save_choice == "oui"
            filename = "resultats_24_formules_main_$(n)_$(Dates.format(now(), "yyyymmdd_HHMMSS")).txt"

            open(filename, "w") do file
                write(file, "RÉSULTATS DES 24 FORMULES POUR LA MAIN $n\n")
                write(file, "="^80 * "\n\n")
                write(file, "Séquence analysée: $(length(sequence)) mains\n")
                write(file, "Date: $(Dates.format(now(), "yyyy-mm-dd HH:MM:SS"))\n\n")

                write(file, "FORMULES OBSERVÉES (OBS):\n")
                write(file, "-"^40 * "\n")
                obs_keys = sort([k for k in keys(resultats) if contains(k, "_OBS")])
                for key in obs_keys
                    write(file, "$key: $(resultats[key])\n")
                end

                write(file, "\nFORMULES THÉORIQUES (THEO):\n")
                write(file, "-"^40 * "\n")
                theo_keys = sort([k for k in keys(resultats) if contains(k, "_THEO")])
                for key in theo_keys
                    write(file, "$key: $(resultats[key])\n")
                end
            end

            println("✅ Résultats sauvegardés dans: $filename")
        end

    catch e
        println("❌ Erreur lors du traitement: $e")
    end
end

"""
    generer_nouveau_tableau_24_formules(sequence::Vector{String}, max_mains::Int = -1) -> String

Génère le nouveau tableau avec les 24 formules organisées :
- Gauche : 12 formules THEO (théoriques)
- Centre : INDEX5_Observé
- Droite : 12 formules OBS (observées)

# Arguments
- `sequence::Vector{String}`: Séquence INDEX5 complète
- `max_mains::Int`: Nombre maximum de mains à afficher (-1 = toutes les mains)

# Returns
- `String`: Tableau formaté prêt à afficher
"""
function generer_nouveau_tableau_24_formules(sequence::Vector{String}, max_mains::Int = -1)
    if isempty(sequence)
        return "❌ Séquence vide"
    end

    # Déterminer le nombre de mains à traiter
    nb_mains = max_mains == -1 ? length(sequence) : min(max_mains, length(sequence))

    # Buffer pour construire le tableau
    tableau = IOBuffer()

    # ═══════════════════════════════════════════════════════════════════════════════
    # EN-TÊTE DU NOUVEAU TABLEAU
    # ═══════════════════════════════════════════════════════════════════════════════

    println(tableau, "\n📊 NOUVEAU TABLEAU - 24 FORMULES D'ENTROPIE:")
    println(tableau, "="^150)

    # En-tête avec TOUTES les 24 formules sur UNE SEULE LIGNE - PARFAITEMENT ALIGNÉ
    println(tableau, @sprintf("%-4s | %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s | %-15s | %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s",
            "Main",
            # 12 formules THEO (gauche)
            "ShannonT", "AEPT", "TauxT", "MetricT", "CondT", "DivKLT",
            "InfoMutT", "CrossT", "TopoT", "BlockT", "CondDecT", "TheoAEPT",
            # Centre
            "INDEX5_Observé",
            # 12 formules OBS (droite)
            "ShannonO", "AEPO", "TauxO", "MetricO", "CondO", "DivKLO",
            "InfoMutO", "CrossO", "TopoO", "BlockO", "CondDecO", "TheoAEPO"))

    println(tableau, "-"^220)

    # ═══════════════════════════════════════════════════════════════════════════════
    # DONNÉES DU TABLEAU
    # ═══════════════════════════════════════════════════════════════════════════════

    for n in 1:nb_mains
        # Calculer toutes les formules pour la main n
        resultats = calculer_toutes_formules_pour_main_n(sequence, n)

        # INDEX5 observé pour cette main
        index5_obs = sequence[n]

        # UNE SEULE LIGNE PARFAITEMENT ALIGNÉE : Main + 12 THEO + INDEX5 + 12 OBS
        println(tableau, @sprintf("%-4d | %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f | %-15s | %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f %-8.4f",
                n,
                # 12 formules THEO (gauche)
                resultats["1B_Shannon_Jointe_THEO"],
                resultats["2B_AEP_THEO"],
                resultats["3B_Taux_Entropie_THEO"],
                resultats["4B_Entropie_Metrique_THEO"],
                resultats["5B_Conditionnelle_THEO"],
                resultats["6B_Divergence_KL_THEO"],
                resultats["7B_Information_Mutuelle_THEO"],
                resultats["8B_Entropie_Croisee_THEO"],
                resultats["9B_Entropie_Topologique_THEO"],
                resultats["10B_Block_Cumulative_THEO"],
                resultats["11B_Conditionnelle_Decroissante_THEO"],
                resultats["12B_Theoreme_AEP_THEO"],
                # Centre
                index5_obs,
                # 12 formules OBS (droite)
                resultats["1A_Shannon_Jointe_OBS"],
                resultats["2A_AEP_OBS"],
                resultats["3A_Taux_Entropie_OBS"],
                resultats["4A_Entropie_Metrique_OBS"],
                resultats["5A_Conditionnelle_OBS"],
                resultats["6A_Divergence_KL_OBS"],
                resultats["7A_Information_Mutuelle_OBS"],
                resultats["8A_Entropie_Croisee_OBS"],
                resultats["9A_Entropie_Topologique_OBS"],
                resultats["10A_Block_Cumulative_OBS"],
                resultats["11A_Conditionnelle_Decroissante_OBS"],
                resultats["12A_Theoreme_AEP_OBS"]))
    end

    println(tableau, "-"^220)
    println(tableau, "📝 LÉGENDE:")
    println(tableau, "   THEO (Gauche) : 12 formules théoriques basées sur le modèle INDEX5")
    println(tableau, "   OBS (Droite)  : 12 formules observées basées sur les fréquences réelles")
    println(tableau, "   Centre        : INDEX5 réellement observé pour chaque main")
    println(tableau, "   Total mains affichées : $nb_mains / $(length(sequence))")

    return String(take!(tableau))
end
#
# Cette section regroupe TOUTES les métriques d'entropie avec leurs méthodes de calcul
# selon les définitions exactes du fichier formules_metriques_entropie_baccarat.txt
#
# ORGANISATION COMPLÈTE DES MÉTRIQUES :
#
# 📊 MÉTRIQUES DE BASE (8 métriques principales) :
# 1. Mt5 (Entropie Mt5 - Kolmogorov-Sinai)
# 2. CONDITIONNELLE (Entropie Conditionnelle)
# 3. T5 (Taux d'Entropie - Entropy Rate)
# 4. ENTROPG (Entropie Générale - AEP avec probabilités théoriques)
# 5. DIVENTROPG (Diversité Entropique - Shannon)
# 6. EGobs (Entropie Générale Observée - AEP avec probabilités observées)
# 7. ConfEG (Conformité Entropique - |EntropG - EGobs|)
# 8. StructEG (Structure Entropique - EntropG/EGobs)
#
# 📈 DIFFÉRENTIELS (7 différentiels dérivés) :
# 9. DIFFCOND/DIFFC (Différentiel Entropie Conditionnelle)
# 10. DIFFT5/DIFFT5 (Différentiel T5)
# 11. DIFFDIVRENTROPG/DIFFDIVEG (Différentiel Diversité Entropique)
# 12. DIFFEG/DIFFEG (Différentiel Entropie Générale)
# 13. DIFFEGOBS/DIFFEGOBS (Différentiel Entropie Générale Observée)
# 14. DIFFSEG/DIFFSEG (Différentiel Structure Entropique)
# 15. DIFFCEG/DIFFCEG (Différentiel Conformité Entropique)
#
# 🎯 SCORE COMPOSITE (1 métrique composite) :
# 16. SCORE = (DiffC + DiffEG) / (DiffT5 + DiffDivEG)
#
# TOTAL : 16 métriques complètes avec toutes leurs méthodes de calcul
# ═══════════════════════════════════════════════════════════════════════════════

# ─────────────────────────────────────────────────────────────────────────────
# 1. Mt5 (Entropie Mt5 - Kolmogorov-Sinai)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_metric_from_t5(analyzer::EntropyAnalyzer{T}, t5_value::T) where T -> T

Calcule la Mt5 à partir d'une valeur T5 déjà calculée : Mt5 = T5 / log₂(18)

Formule exacte : Mt5 = T5 / log₂(18)
Où log₂(18) est l'entropie maximale uniforme pour 18 valeurs INDEX5.

Cette fonction évite le recalcul de T5 en utilisant la valeur déjà disponible.
"""
function calculate_metric_from_t5(analyzer::EntropyAnalyzer{T}, t5_value::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # Mt5 = T5 / log₂(18) - Calcul exact
    # log2_18 = log(T(18)) / log(analyzer.base)  # log₂(18) calculé exactement
    # if log2_18 ≈ zero(T)
    #     return zero(T)
    # end
    # return t5_value / log2_18

    # Valeur par défaut temporaire
    return zero(T)
end

"""
    calculate_metric_entropy_new(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

ANCIENNE FONCTION - Conservée pour compatibilité temporaire
Calcule la Mt5 simplifiée : Mt5 = T5 / 4.1699
"""
function calculate_metric_entropy_new(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # Calculer T5 avec probabilités théoriques
    # t5 = calculate_entropy_rate_new(analyzer, sequence)
    # Mt5 = T5 / log₂(18)
    # return calculate_metric_from_t5(analyzer, t5)

    # Valeur par défaut temporaire
    return zero(T)
end

"""
    estimate_metric_entropy(block_entropies::Vector{T}) where T -> T

ANCIENNE MÉTHODE - Conservée pour compatibilité temporaire
Estime l'entropie Mt5 (Kolmogorov-Sinai) : h_μ(T) ≈ H(max_length)/max_length

Formule théorique : h_μ(T) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
Implémentation pratique : h_metric = H(max_length)/max_length
"""
function estimate_metric_entropy(block_entropies::Vector{T}) where T<:AbstractFloat
    if isempty(block_entropies)
        return zero(T)
    end

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # Approximation de la limite : h_metric = H(max_length)/max_length
    # max_length = length(block_entropies)
    # return block_entropies[end] / max_length

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 2. CONDITIONNELLE (Entropie Conditionnelle)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_conditional_entropy(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Calcule l'entropie conditionnelle H(Xₙ|Xₙ₋₁) avec contexte de longueur 1.

Formule théorique : H(X|Y) = ∑ P(y) × H(X|Y=y) = -∑∑ P(x,y) × log₂ P(x|y)
Implémentation pratique : Contexte de longueur 1 : H(Xₙ|Xₙ₋₁)
"""
function calculate_conditional_entropy(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if length(sequence) < 2
        return zero(T)
    end

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # Compter les transitions contexte → symbole suivant
    # context_transitions = Dict{String, Dict{String, Int}}()
    # for i in 1:(length(sequence)-1)
    #     context = sequence[i]
    #     next_symbol = sequence[i+1]
    #     if !haskey(context_transitions, context)
    #         context_transitions[context] = Dict{String, Int}()
    #     end
    #     context_transitions[context][next_symbol] =
    #         get(context_transitions[context], next_symbol, 0) + 1
    # end
    # # Calculer H(X|Contexte) = ∑ P(contexte) × H(X|contexte)
    # total_transitions = sum(sum(values(transitions)) for transitions in values(context_transitions))
    # conditional_entropy = zero(T)
    # for (context, transitions) in context_transitions
    #     context_prob = T(sum(values(transitions))) / T(total_transitions)
    #     # Créer séquence des symboles suivants pour ce contexte
    #     context_sequence = String[]
    #     for (next_symbol, count) in transitions
    #         append!(context_sequence, fill(next_symbol, count))
    #     end
    #     # H(X|ce contexte) calculé selon AEP
    #     context_entropy = calculate_sequence_entropy_aep(analyzer, context_sequence)
    #     conditional_entropy += context_prob * context_entropy
    # end
    # return conditional_entropy

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 3. T5 (Taux d'Entropie - Entropy Rate)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_entropy_rate_new(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Calcule le T5 selon la nouvelle méthode théoriquement correcte.

Nouvelle formule : T5 = H_AEP(fenêtre_courante_de_5_éléments)
Où fenêtre_courante = [Xₙ₋₄, Xₙ₋₃, Xₙ₋₂, Xₙ₋₁, Xₙ]

Cette méthode calcule l'entropie jointe H₅ de la fenêtre de 5 éléments
au lieu de faire la moyenne des fenêtres glissantes.
"""
function calculate_entropy_rate_new(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T<:AbstractFloat
    # Vérifier qu'on a au moins 5 éléments
    if length(sequence) < 5
        return zero(T)
    end

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # Extraire la fenêtre de 5 derniers éléments
    # window_5 = sequence[(end-4):end]
    # Calculer H₅ = H_AEP de la fenêtre de 5
    # h5 = calculate_sequence_entropy_aep(analyzer, window_5)
    # T5 = H₅ directement (pas de division supplémentaire)
    # return h5

    # Valeur par défaut temporaire
    return zero(T)
end

"""
    calculate_entropy_rate(block_entropies::Vector{T}) where T -> T

ANCIENNE MÉTHODE - Conservée pour compatibilité temporaire
Calcule le T5 : entropy_rate = block_entropies[-1]

Formule théorique : H_rate = lim_{n→∞} (1/n) H(X₁, X₂, ..., Xₙ)
Implémentation pratique : T5 = block_entropies[-1] (entropie du dernier bloc)
"""
function calculate_entropy_rate(block_entropies::Vector{T}) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return isempty(block_entropies) ? zero(T) : block_entropies[end]

    # Valeur par défaut temporaire
    return zero(T)
end

"""
    calculate_block_entropies(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}, max_length::Int) where T -> Vector{T}

Calcule les entropies de blocs de longueurs croissantes pour le calcul du T5.
Utilisé par les métriques Mt5 et T5 (ancienne méthode - conservée pour compatibilité).
"""
function calculate_block_entropies(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    max_length::Int
) where T<:AbstractFloat
    entropies = T[]

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # for block_len in 1:min(max_length, length(sequence))
    #     if block_len == 1
    #         # Pour longueur 1 : entropie AEP de la séquence complète
    #         block_entropy = calculate_sequence_entropy_aep(analyzer, sequence)
    #     else
    #         # Pour longueur > 1 : entropie moyenne des sous-séquences
    #         total_entropy = zero(T)
    #         num_blocks = 0
    #         for i in 1:(length(sequence) - block_len + 1)
    #             block_sequence = sequence[i:(i + block_len - 1)]
    #             total_entropy += calculate_sequence_entropy_aep(analyzer, block_sequence)
    #             num_blocks += 1
    #         end
    #         block_entropy = num_blocks > 0 ? total_entropy / num_blocks : zero(T)
    #     end
    #     push!(entropies, block_entropy)
    # end

    # Valeur par défaut temporaire
    for i in 1:min(max_length, length(sequence))
        push!(entropies, zero(T))
    end

    return entropies
end

# ─────────────────────────────────────────────────────────────────────────────
# 4. ENTROPG (Entropie Générale - AEP)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_sequence_entropy_aep(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Calcule l'entropie d'une séquence selon la formule AEP (Asymptotic Equipartition Property).

Formule théorique AEP : H_AEP = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))
Pour séquences indépendantes : p(X₁, X₂, ..., Xₙ) = ∏ᵢ₌₁ⁿ p_théo(xᵢ)
"""
function calculate_sequence_entropy_aep(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # total_log_prob = zero(T)
    # for value in sequence
    #     p_theo = get(analyzer.theoretical_probs, value, zero(T))
    #     total_log_prob += safe_log(p_theo, analyzer.base, analyzer.epsilon)
    # end
    # return -total_log_prob / length(sequence)

    # Valeur par défaut temporaire
    return zero(T)
end

"""
    calculate_sequence_entropy_aep_observed(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Calcule l'entropie d'une séquence selon la formule AEP avec probabilités observées (EGobs).

Formule AEP observé : H_AEP_obs = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_obs(xᵢ))
où p_obs(xᵢ) = fréquence observée de xᵢ dans la séquence / longueur totale

Cette métrique mesure l'information moyenne par main selon les patterns réellement observés,
sans référence au modèle théorique INDEX5.
"""
function calculate_sequence_entropy_aep_observed(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # 1. Compter les occurrences de chaque valeur
    # counts = Dict{String, Int}()
    # for value in sequence
    #     counts[value] = get(counts, value, 0) + 1
    # end
    # 2. Calculer la somme des logarithmes avec probabilités observées
    # n = length(sequence)
    # total_log_prob = zero(T)
    # for value in sequence
    #     p_obs = T(counts[value]) / T(n)  # Probabilité observée
    #     total_log_prob += safe_log(p_obs, analyzer.base, analyzer.epsilon)
    # end
    # 3. Appliquer la formule AEP observé
    # return -total_log_prob / n

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 5. DIVENTROPG (Diversité Entropique - Shannon)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_shannon_entropy(analyzer::EntropyAnalyzer{T}, probabilities::Vector{T}) where T -> T

Calcule l'entropie de Shannon (DivEntropG) : H(X) = -∑ p_obs(x) log₂ p_obs(x)

Formule théorique : H(X) = -∑ p_obs(x) log₂ p_obs(x)
où p_obs(x) = fréquence observée de x / longueur totale
"""
function calculate_shannon_entropy(
    analyzer::EntropyAnalyzer{T},
    probabilities::Vector{T}
) where T<:AbstractFloat
    probs = validate_probabilities(probabilities)

    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # entropy = zero(T)
    # for p in probs
    #     if p > zero(T)
    #         entropy -= p * safe_log(p, analyzer.base, analyzer.epsilon)
    #     end
    # end
    # return entropy

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 6. EGobs (Entropie Générale Observée - AEP avec probabilités observées)
# ─────────────────────────────────────────────────────────────────────────────

# La fonction calculate_sequence_entropy_aep_observed est définie plus haut dans la section 4. ENTROPG

# ─────────────────────────────────────────────────────────────────────────────
# 7. ConfEG et StructEG (Métriques de Conformité et Structure)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_conf_eg(entrop_g::T, eg_obs::T) where T -> T

Calcule ConfEG = |EntropG - EGobs| (Conformité Entropique).

Formule : ConfEG = |H_AEP_théo - H_AEP_obs|
Cette métrique mesure l'écart absolu entre l'information théorique attendue
selon le modèle INDEX5 et l'information réelle selon les patterns observés.

Interprétation :
- ≈ 0.0 : Parfaite conformité au modèle
- 0.1-0.5 : Légère déviation
- 0.5-1.0 : Déviation modérée
- > 1.0 : Forte déviation, anomalie détectée
"""
function calculate_conf_eg(entrop_g::T, eg_obs::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(entrop_g - eg_obs)

    # Valeur par défaut temporaire
    return zero(T)
end

"""
    calculate_struct_eg(entrop_g::T, eg_obs::T) where T -> T

Calcule StructEG = EntropG/EGobs (Structure Entropique).

Formule : StructEG = H_AEP_théo / H_AEP_obs
Cette métrique mesure le facteur multiplicatif entre l'information théorique
et l'information observée.

Interprétation :
- ≈ 1.0 : Équilibre parfait (Réalité = Théorie)
- > 1.0 : Surestimation théorique (séquence plus prévisible que prévu)
- < 1.0 : Sous-estimation théorique (séquence plus complexe que prévu)
- >> 2.0 : Patterns très répétitifs détectés
- << 0.5 : Chaos apparent, complexité extrême
"""
function calculate_struct_eg(entrop_g::T, eg_obs::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # if eg_obs ≈ zero(T)
    #     return T(Inf)  # Division par zéro -> infini
    # end
    # return entrop_g / eg_obs

    # Valeur par défaut temporaire
    return one(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 9. DIFFCOND/DIFFC (Différentiel Entropie Conditionnelle)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_diff_cond(current_conditional::T, previous_conditional::T) where T -> T

Calcule DiffC = DiffCond (Différentiel Entropie Conditionnelle).

Formule : DiffCond(n) = |Conditionnelle(n) - Conditionnelle(n-1)|
DiffC = DiffCond (abréviation dans les tableaux)
"""
function calculate_diff_cond(current_conditional::T, previous_conditional::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(current_conditional - previous_conditional)

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 10. DIFFT5/DIFFT5 (Différentiel T5)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_diff_taux(current_rate::T, previous_rate::T) where T -> T

Calcule DiffT5 = DiffT5 (Différentiel T5).

Formule : DiffT5(n) = |T5(n) - T5(n-1)|
DiffT5 = DiffT5 (abréviation dans les tableaux)
"""
function calculate_diff_taux(current_rate::T, previous_rate::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(current_rate - previous_rate)

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 11. DIFFDIVRENTROPG/DIFFDIVEG (Différentiel Diversité Entropique)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_diff_div_entrop_g(current_simple::T, previous_simple::T) where T -> T

Calcule DiffDivEG = DiffDivEntropG (Différentiel Diversité Entropique).

Formule : DiffDivEntropG(n) = |DivEntropG(n) - DivEntropG(n-1)|
                            = |simple_entropy(n) - simple_entropy(n-1)|
DiffDivEG = DiffDivEntropG (abréviation dans les tableaux)
"""
function calculate_diff_div_entrop_g(current_simple::T, previous_simple::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(current_simple - previous_simple)

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 12. DIFFEG/DIFFEG (Différentiel Entropie Générale)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_diff_entrop_g(current_theoretical::T, previous_theoretical::T) where T -> T

Calcule DiffEG = DiffEG (Différentiel Entropie Générale).

Formule : DiffEG(n) = |EntropG(n) - EntropG(n-1)|
                         = |simple_entropy_theoretical(n) - simple_entropy_theoretical(n-1)|
DiffEG = DiffEG (abréviation dans les tableaux)
"""
function calculate_diff_entrop_g(current_theoretical::T, previous_theoretical::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(current_theoretical - previous_theoretical)

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 13. DIFFEGOBS/DIFFEGOBS (Différentiel Entropie Générale Observée)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_diff_egobs(current_egobs::T, previous_egobs::T) where T -> T

Calcule DiffEGobs = DiffEGobs (Différentiel Entropie Générale Observée).

Formule : DiffEGobs(n) = |EGobs(n) - EGobs(n-1)|
où EGobs = entropy_aep_observed (Entropie AEP avec probabilités observées)
"""
function calculate_diff_egobs(current_egobs::T, previous_egobs::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(current_egobs - previous_egobs)

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 14. DIFFSEG/DIFFSEG (Différentiel Structure Entropique)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_diff_seg(current_struct_eg::T, previous_struct_eg::T) where T -> T

Calcule DiffSEG = DiffSEG (Différentiel Structure Entropique).

Formule : DiffSEG(n) = |StructEG(n) - StructEG(n-1)|
où StructEG = EntropG/EGobs (Structure Entropique)
"""
function calculate_diff_seg(current_struct_eg::T, previous_struct_eg::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(current_struct_eg - previous_struct_eg)

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 15. DIFFCEG/DIFFCEG (Différentiel Conformité Entropique)
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_diff_ceg(current_conf_eg::T, previous_conf_eg::T) where T -> T

Calcule DiffCEG = DiffCEG (Différentiel Conformité Entropique).

Formule : DiffCEG(n) = |ConfEG(n) - ConfEG(n-1)|
où ConfEG = |EntropG - EGobs| (Conformité Entropique)
"""
function calculate_diff_ceg(current_conf_eg::T, previous_conf_eg::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # return abs(current_conf_eg - previous_conf_eg)

    # Valeur par défaut temporaire
    return zero(T)
end

# ─────────────────────────────────────────────────────────────────────────────
# 16. SCORE COMPOSITE
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculate_predictive_score(mt5::T, diff_eg::T, diff_div_eg::T, diff_c::T, diff_seg::T, diff_ceg::T, diff_t5::T) where T -> T

Calcule le score prédictif composite selon la FORMULE ENTROPIQUE OPTIMALE.

FORMULE ENTROPIQUE OPTIMALE :
SCORE = Mt5 × exp(-DiffEG) × exp(-DiffDivEG) × exp(-DiffC) / ((1 + DiffSEG + DiffCEG) × (1 + DiffT5))

Cette formule combine :
- Mt5 : Complexité informationnelle normalisée (facteur multiplicatif)
- exp(-DiffEG) : Régularisation de la variation information théorique
- exp(-DiffDivEG) : Régularisation de la variation diversité observée
- exp(-DiffC) : Régularisation de la variation prédictibilité contextuelle
- (1 + DiffSEG + DiffCEG) : Stabilisation cohérence théorie/réalité
- (1 + DiffT5) : Contrôle des fluctuations temporelles

Parameters:
    mt5: Mt5 (Entropie Métrique normalisée)
    diff_eg: DiffEG (Variation information théorique)
    diff_div_eg: DiffDivEG (Variation diversité observée)
    diff_c: DiffC (Variation prédictibilité contextuelle)
    diff_seg: DiffSEG (Variation ratio théorie/réalité)
    diff_ceg: DiffCEG (Variation distance théorie/réalité)
    diff_t5: DiffT5 (Variation taux entropique)

Returns:
    SCORE: Score prédictif selon la formule entropique optimale
"""
function calculate_predictive_score(mt5::T, diff_eg::T, diff_div_eg::T, diff_c::T, diff_seg::T, diff_ceg::T, diff_t5::T) where T<:AbstractFloat
    # FORMULE DÉSACTIVÉE - Voir section FORMULES_DÉSACTIVÉES
    # Calcul des termes exponentiels de régularisation
    # exp_diff_eg = exp(-diff_eg)
    # exp_diff_div_eg = exp(-diff_div_eg)
    # exp_diff_c = exp(-diff_c)
    # Calcul du dénominateur : (1 + DiffSEG + DiffCEG) × (1 + DiffT5)
    # denominator = (one(T) + diff_seg + diff_ceg) * (one(T) + diff_t5)
    # Gestion du cas dénominateur ≈ 0 (très improbable avec cette formule)
    # if denominator ≈ zero(T)
    #     return T(Inf)  # Score infini
    # else
    #     # Calcul du score selon la formule entropique optimale
    #     numerator = mt5 * exp_diff_eg * exp_diff_div_eg * exp_diff_c
    #     return numerator / denominator
    # end

    # Valeur par défaut temporaire
    return zero(T)
end

# ═══════════════════════════════════════════════════════════════════════════════
# ANALYSE ENTROPIQUE AVANCÉE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculate_block_entropies(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}, max_length::Int) where T -> Vector{T}

Calcule les entropies de blocs de longueurs croissantes.
"""
function calculate_block_entropies(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    max_length::Int
) where T<:AbstractFloat
    entropies = T[]

    for block_len in 1:min(max_length, length(sequence))
        if block_len == 1
            # Pour longueur 1 : entropie AEP de la séquence complète
            block_entropy = calculate_sequence_entropy_aep(analyzer, sequence)
        else
            # Pour longueur > 1 : entropie moyenne des sous-séquences
            total_entropy = zero(T)
            num_blocks = 0

            for i in 1:(length(sequence) - block_len + 1)
                block_sequence = sequence[i:(i + block_len - 1)]
                total_entropy += calculate_sequence_entropy_aep(analyzer, block_sequence)
                num_blocks += 1
            end

            block_entropy = num_blocks > 0 ? total_entropy / num_blocks : zero(T)
        end

        push!(entropies, block_entropy)
    end

    return entropies
end

"""
    calculate_all_metrics_evolution(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}, max_block_length::Int = 5) where T -> Vector{EntropyMetrics{T}}

Calcule l'évolution de TOUTES les métriques d'entropie pour chaque position dans la séquence.
Cette fonction centrale utilise toutes les métriques définies dans cette section.

Métriques calculées pour chaque position :
- Mt5 : Entropie Mt5 (nouvelle méthode avec entropie jointe de fenêtre de 5)
- CONDITIONNELLE : Entropie conditionnelle H(Xₙ|Xₙ₋₁)
- T5 : Taux d'entropie (nouvelle méthode avec entropie jointe de fenêtre de 5)
- ENTROPG : Entropie générale (AEP avec probabilités théoriques)
- DIVENTROPG : Diversité entropique (Shannon avec fréquences observées)
- EGobs : Entropie générale observée (AEP avec probabilités observées)
- ConfEG : Conformité entropique (|EntropG - EGobs|)
- StructEG : Structure entropique (EntropG/EGobs)
"""
function calculate_all_metrics_evolution(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    max_block_length::Int = 5
) where T<:AbstractFloat
    if isempty(sequence)
        return EntropyMetrics{T}[]
    end

    results = EntropyMetrics{T}[]

    for n in 1:length(sequence)
        # Sous-séquence jusqu'à la position n
        subsequence = sequence[1:n]

        # ═══ CALCUL DE TOUTES LES MÉTRIQUES DE BASE ═══

        # 5. DIVENTROPG : Diversité entropique (Shannon avec fréquences observées)
        counts = Dict{String, Int}()
        for value in subsequence
            counts[value] = get(counts, value, 0) + 1
        end
        empirical_probs = T[counts[value] / n for value in keys(counts)]
        simple_entropy_observed = calculate_shannon_entropy(analyzer, empirical_probs)

        # 4. ENTROPG : Entropie générale (AEP avec probabilités théoriques)
        simple_entropy_theoretical = calculate_sequence_entropy_aep(analyzer, subsequence)

        # 2. CONDITIONNELLE : Entropie conditionnelle H(Xₙ|Xₙ₋₁)
        conditional_entropy = calculate_conditional_entropy(analyzer, subsequence)

        # Calcul des entropies de blocs (plus nécessaire - Mt5 et T5 utilisent maintenant leurs propres méthodes)
        block_entropies = calculate_block_entropies(analyzer, subsequence, max_block_length)

        # 3. T5 : Taux d'entropie (entropy rate) - NOUVELLE MÉTHODE
        entropy_rate = calculate_entropy_rate_new(analyzer, subsequence)

        # 1. Mt5 : Entropie Mt5 (Kolmogorov-Sinai) - CALCULÉE À PARTIR DE T5
        metric_entropy = calculate_metric_from_t5(analyzer, entropy_rate)

        # 6. EGobs : Entropie générale observée (AEP avec probabilités observées)
        entropy_aep_observed = calculate_sequence_entropy_aep_observed(analyzer, subsequence)

        # 7. ConfEG : Conformité entropique |EntropG - EGobs|
        conf_eg = calculate_conf_eg(simple_entropy_theoretical, entropy_aep_observed)

        # 8. StructEG : Structure entropique EntropG/EGobs
        struct_eg = calculate_struct_eg(simple_entropy_theoretical, entropy_aep_observed)

        # Créer la structure de métriques complète
        metrics = EntropyMetrics{T}(
            n,                              # position
            n,                              # sequence_length
            length(counts),                 # unique_values
            simple_entropy_observed,        # simple_entropy (DIVENTROPG)
            simple_entropy_theoretical,     # simple_entropy_theoretical (ENTROPG)
            conditional_entropy,            # conditional_entropy (CONDITIONNELLE)
            metric_entropy,                 # metric_entropy (Mt5)
            entropy_rate,                   # entropy_rate (T5)
            entropy_aep_observed,           # entropy_aep_observed (EGobs)
            conf_eg,                        # conf_eg (ConfEG)
            struct_eg,                      # struct_eg (StructEG)
            block_entropies                 # block_entropies (pour calculs avancés)
        )

        push!(results, metrics)
    end

    return results
end

# ═══════════════════════════════════════════════════════════════════════════════
# FIN DE LA SECTION DÉDIÉE - TOUTES LES MÉTRIQUES D'ENTROPIE BACCARAT
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section contient maintenant TOUTES les métriques d'entropie :
# ✅ 8 MÉTRIQUES DE BASE : Mt5, Conditionnelle, T5, EntropG, DivEntropG, EGobs, ConfEG, StructEG
# ✅ 7 DIFFÉRENTIELS : DiffCond/DiffC, DiffT5/DiffT5, DiffDivEntropG/DiffDivEG, DiffEG/DiffEG, DiffEGobs, DiffSEG, DiffCEG
# ✅ 1 SCORE COMPOSITE : SCORE = (DiffC + DiffEG) / (DiffT5 + DiffDivEG)
# ✅ FONCTIONS UTILITAIRES : Calcul des blocs, évolution complète des métriques
#
# TOTAL : 16 métriques + fonctions de support = Section complète et autonome
# ═══════════════════════════════════════════════════════════════════════════════

# ═══════════════════════════════════════════════════════════════════════════════
# RÈGLES INDEX1 DÉTERMINISTES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculate_required_index1(current_index5::String) -> Union{Int, Nothing}

Calcule INDEX1 obligatoire selon les règles déterministes:
- Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
- Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)
"""
function calculate_required_index1(current_index5::String)
    if isempty(current_index5)
        return nothing
    end
    
    try
        parts = split(current_index5, '_')
        if length(parts) < 2
            return nothing
        end
        
        current_index1 = parse(Int, parts[1])
        current_index2 = parts[2]
        
        if current_index2 == "C"
            return 1 - current_index1  # Inversion obligatoire
        else  # A ou B
            return current_index1      # Conservation obligatoire
        end
    catch
        return nothing
    end
end

"""
    get_valid_index5_values(required_index1::Int) -> Vector{String}

Retourne les 9 valeurs INDEX5 possibles avec INDEX1 obligatoire.
"""
function get_valid_index5_values(required_index1::Int)
    valid_values = String[]
    for index2 in ["A", "B", "C"]
        for index3 in ["BANKER", "PLAYER", "TIE"]
            push!(valid_values, "$(required_index1)_$(index2)_$(index3)")
        end
    end
    return valid_values
end

# ═══════════════════════════════════════════════════════════════════════════════
# CHARGEMENT ET EXTRACTION DES DONNÉES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    load_baccarat_data(filepath::String) -> Vector{Dict}

Charge les données de baccarat depuis un fichier JSON.
Gère différentes structures JSON possibles.
"""
function load_baccarat_data(filepath::String)
    try
        data = JSON.parsefile(filepath)
        
        # Vérifier la structure du JSON
        if isa(data, Dict) && haskey(data, "parties_condensees")
            parties = data["parties_condensees"]
            @info "✅ Données chargées: $(length(parties)) parties trouvées"
            return parties
        elseif isa(data, Vector)
            @info "✅ Données chargées: $(length(data)) parties trouvées"
            return data
        else
            @warn "❌ Structure JSON non reconnue"
            return Dict[]
        end
    catch e
        if isa(e, SystemError)
            @error "❌ Erreur: Fichier $filepath non trouvé"
        else
            @error "❌ Erreur JSON: $e"
        end
        return Dict[]
    end
end

"""
    extract_index5_sequence(game_data::Dict) -> Vector{String}

Extrait la séquence INDEX5 d'une partie.
Filtre les mains d'ajustement et extrait uniquement les valeurs INDEX5 valides.
"""
function extract_index5_sequence(game_data::Dict)
    sequence = String[]
    
    # Vérifier différentes structures possibles
    if haskey(game_data, "hands")
        # Structure: {"hands": [...]}
        for hand in game_data["hands"]
            # Exclure les mains d'ajustement
            if (haskey(hand, "main_number") && 
                !isnothing(hand["main_number"]) &&
                haskey(hand, "INDEX5") && 
                !isnothing(hand["INDEX5"]) &&
                !isempty(strip(string(hand["INDEX5"]))))
                push!(sequence, string(hand["INDEX5"]))
            end
        end
    elseif haskey(game_data, "mains_condensees")
        # Structure: {"mains_condensees": [...]}
        for main in game_data["mains_condensees"]
            # Exclure les mains d'ajustement
            if (haskey(main, "main_number") && 
                !isnothing(main["main_number"]) &&
                haskey(main, "index5") && 
                !isnothing(main["index5"]) &&
                !isempty(strip(string(main["index5"]))))
                push!(sequence, string(main["index5"]))
            end
        end
    else
        @warn "❌ Structure de partie non reconnue. Clés disponibles: $(keys(game_data))"
        return String[]
    end
    
    @info "🔍 Séquence extraite: $(length(sequence)) mains valides"
    return sequence
end

# ═══════════════════════════════════════════════════════════════════════════════
# ANALYSE ENTROPIQUE AVANCÉE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculate_block_entropies(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}, max_length::Int) where T -> Vector{T}

Calcule les entropies de blocs de longueurs croissantes.
"""
function calculate_block_entropies(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    max_length::Int
) where T<:AbstractFloat
    entropies = T[]

    for block_len in 1:min(max_length, length(sequence))
        if block_len == 1
            # Pour longueur 1 : entropie AEP de la séquence complète
            block_entropy = calculate_sequence_entropy_aep(analyzer, sequence)
        else
            # Pour longueur > 1 : entropie moyenne des sous-séquences
            total_entropy = zero(T)
            num_blocks = 0

            for i in 1:(length(sequence) - block_len + 1)
                block_sequence = sequence[i:(i + block_len - 1)]
                total_entropy += calculate_sequence_entropy_aep(analyzer, block_sequence)
                num_blocks += 1
            end

            block_entropy = num_blocks > 0 ? total_entropy / num_blocks : zero(T)
        end

        push!(entropies, block_entropy)
    end

    return entropies
end



"""
    calculate_block_entropy_evolution(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}, max_block_length::Int = 5) where T -> Vector{EntropyMetrics{T}}

Calcule l'évolution de l'entropie par blocs pour chaque position dans la séquence.
"""
function calculate_block_entropy_evolution(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    max_block_length::Int = 5
) where T<:AbstractFloat
    if isempty(sequence)
        return EntropyMetrics{T}[]
    end

    results = EntropyMetrics{T}[]

    for n in 1:length(sequence)
        # Sous-séquence jusqu'à la position n
        subsequence = sequence[1:n]

        # Compter les occurrences pour l'entropie empirique
        counts = Dict{String, Int}()
        for value in subsequence
            counts[value] = get(counts, value, 0) + 1
        end

        # Probabilités empiriques
        empirical_probs = T[counts[value] / n for value in keys(counts)]

        # Entropie de Shannon sur fréquences observées
        simple_entropy_observed = calculate_shannon_entropy(analyzer, empirical_probs)

        # Entropie AEP avec probabilités théoriques
        simple_entropy_theoretical = calculate_sequence_entropy_aep(analyzer, subsequence)

        # Entropie conditionnelle
        conditional_entropy = calculate_conditional_entropy(analyzer, subsequence)

        # Entropies de blocs
        block_entropies = calculate_block_entropies(analyzer, subsequence, max_block_length)

        # T5 (nouvelle méthode - entropie jointe de la fenêtre de 5)
        entropy_rate = calculate_entropy_rate_new(analyzer, sequence)

        # Entropie Mt5 (calculée à partir de T5 pour éviter le double calcul)
        metric_entropy = calculate_metric_from_t5(analyzer, entropy_rate)

        # EGobs (entropie générale observée avec probabilités observées)
        entropy_aep_observed = calculate_sequence_entropy_aep_observed(analyzer, sequence)

        # ConfEG et StructEG (métriques de conformité et structure)
        conf_eg = calculate_conf_eg(simple_entropy_theoretical, entropy_aep_observed)
        struct_eg = calculate_struct_eg(simple_entropy_theoretical, entropy_aep_observed)

        # Créer la structure de métriques
        metrics = EntropyMetrics{T}(
            n,                              # position
            n,                              # sequence_length
            length(counts),                 # unique_values
            simple_entropy_observed,        # simple_entropy
            simple_entropy_theoretical,     # simple_entropy_theoretical
            conditional_entropy,            # conditional_entropy
            metric_entropy,                 # metric_entropy
            entropy_rate,                   # entropy_rate
            entropy_aep_observed,           # entropy_aep_observed (EGobs)
            conf_eg,                        # conf_eg (ConfEG)
            struct_eg,                      # struct_eg (StructEG)
            block_entropies                 # block_entropies
        )

        push!(results, metrics)
    end

    return results
end

# ═══════════════════════════════════════════════════════════════════════════════
# CALCULS DIFFÉRENTIELS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculate_differentials(entropy_evolution::Vector{EntropyMetrics{T}}) where T -> Vector{PredictiveDifferentials{T}}

Calcule les différentiels entre mains consécutives.
"""
function calculate_differentials(
    entropy_evolution::Vector{EntropyMetrics{T}}
) where T<:AbstractFloat
    if length(entropy_evolution) < 2
        return PredictiveDifferentials{T}[]
    end

    differentials = PredictiveDifferentials{T}[]

    # Première main : différentiel = 0 (pas de main précédente)
    first_diff = PredictiveDifferentials{T}(
        zero(T), zero(T), zero(T), zero(T), zero(T), zero(T), zero(T), zero(T)  # DiffC, DiffT5, DiffDivEG, DiffEG, DiffEGobs, DiffSEG, DiffCEG, SCORE
    )
    push!(differentials, first_diff)

    # Calculer les différentiels pour les mains suivantes
    for i in 2:length(entropy_evolution)
        current = entropy_evolution[i]
        previous = entropy_evolution[i-1]

        # Calcul des différentiels selon les définitions exactes en utilisant les fonctions dédiées
        diff_cond = calculate_diff_cond(current.conditional_entropy, previous.conditional_entropy)  # DiffC = DiffCond
        diff_taux = calculate_diff_taux(current.entropy_rate, previous.entropy_rate)               # DiffT5 = DiffT5
        diff_div_entrop_g = calculate_diff_div_entrop_g(current.simple_entropy, previous.simple_entropy)   # DiffDivEG = DiffDivEntropG
        diff_entrop_g = calculate_diff_entrop_g(current.simple_entropy_theoretical, previous.simple_entropy_theoretical)  # DiffEG = DiffEG

        # Calcul des nouveaux différentiels
        diff_egobs = calculate_diff_egobs(current.entropy_aep_observed, previous.entropy_aep_observed)  # DiffEGobs
        diff_seg = calculate_diff_seg(current.struct_eg, previous.struct_eg)                           # DiffSEG
        diff_ceg = calculate_diff_ceg(current.conf_eg, previous.conf_eg)                               # DiffCEG

        # Calcul du score composite selon la formule entropique optimale
        # SCORE = Mt5 × exp(-DiffEG) × exp(-DiffDivEG) × exp(-DiffC) / ((1 + DiffSEG + DiffCEG) × (1 + DiffT5))
        score = calculate_predictive_score(
            current.metric_entropy,     # Mt5
            diff_entrop_g,              # DiffEG
            diff_div_entrop_g,          # DiffDivEG
            diff_cond,                  # DiffC
            diff_seg,                   # DiffSEG
            diff_ceg,                   # DiffCEG
            diff_taux                   # DiffT5
        )

        diff = PredictiveDifferentials{T}(
            diff_cond, diff_taux, diff_div_entrop_g, diff_entrop_g, diff_egobs, diff_seg, diff_ceg, score
        )
        push!(differentials, diff)
    end

    return differentials
end

# ═══════════════════════════════════════════════════════════════════════════════
# ANALYSE COMPLÈTE D'UNE PARTIE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    analyze_single_game(analyzer::EntropyAnalyzer{T}, game_data::Dict, game_id::Union{String, Nothing} = nothing) where T -> Dict

Analyse complète d'une seule partie selon les méthodes avancées d'entropie.
"""
function analyze_single_game(
    analyzer::EntropyAnalyzer{T},
    game_data::Dict,
    game_id::Union{String, Nothing} = nothing
) where T<:AbstractFloat
    sequence = extract_index5_sequence(game_data)

    if isempty(sequence)
        return Dict("error" => "Aucune séquence INDEX5 trouvée")
    end

    # Calcul de l'évolution entropique en utilisant la fonction centralisée des métriques
    entropy_evolution = calculate_all_metrics_evolution(analyzer, sequence, 4)

    if isempty(entropy_evolution)
        return Dict("error" => "Impossible de calculer l'évolution d'entropie")
    end

    # Calcul des différentiels
    differentials = calculate_differentials(entropy_evolution)

    # Métriques finales
    final_metrics = entropy_evolution[end]

    # Statistiques de la séquence
    unique_values = Set(sequence)
    value_counts = Dict{String, Int}()
    for value in sequence
        value_counts[value] = get(value_counts, value, 0) + 1
    end

    # Résultats complets
    results = Dict(
        "game_id" => game_id,
        "sequence_length" => length(sequence),
        "unique_values_count" => length(unique_values),
        "sequence" => sequence,
        "entropy_evolution" => entropy_evolution,
        "differentials" => differentials,
        "final_metrics" => final_metrics,
        "value_counts" => value_counts,
        "analysis_summary" => Dict(
            "final_shannon_entropy" => final_metrics.simple_entropy,
            "final_theoretical_entropy" => final_metrics.simple_entropy_theoretical,
            "final_conditional_entropy" => final_metrics.conditional_entropy,
            "final_metric_entropy" => final_metrics.metric_entropy,
            "entropy_rate" => final_metrics.entropy_rate
        )
    )

    return results
end

# ═══════════════════════════════════════════════════════════════════════════════
# SYSTÈME DE PRÉDICTION
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculate_predictive_differentials(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}, position::Int) where T -> Dict{String, PredictiveDifferentials{T}}

Calcule les différentiels prédictifs pour toutes les valeurs INDEX5 possibles.
"""
function calculate_predictive_differentials(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    position::Int
) where T<:AbstractFloat
    if position < 1 || position > length(sequence)
        return Dict{String, PredictiveDifferentials{T}}()
    end

    # Calculer l'évolution entropique jusqu'à la position actuelle en utilisant la fonction centralisée
    current_sequence = sequence[1:position]
    evolution = calculate_all_metrics_evolution(analyzer, current_sequence, 4)

    if isempty(evolution)
        return Dict{String, PredictiveDifferentials{T}}()
    end

    current_metrics = evolution[end]

    # Obtenir les valeurs INDEX5 possibles selon les règles INDEX1
    current_index5 = sequence[position]
    required_index1 = calculate_required_index1(current_index5)

    if isnothing(required_index1)
        return Dict{String, PredictiveDifferentials{T}}()
    end

    valid_index5_values = get_valid_index5_values(required_index1)
    predictive_differentials = Dict{String, PredictiveDifferentials{T}}()

    # Pour chaque valeur INDEX5 possible
    for possible_index5 in valid_index5_values
        # Simuler l'ajout de cette valeur
        simulated_sequence = vcat(current_sequence, [possible_index5])

        # Calculer les nouvelles métriques simulées
        simulated_conditional = calculate_conditional_entropy(analyzer, simulated_sequence)

        # Entropie simple observée simulée
        counts = Dict{String, Int}()
        for value in simulated_sequence
            counts[value] = get(counts, value, 0) + 1
        end
        empirical_probs = T[counts[value] / length(simulated_sequence) for value in keys(counts)]
        simulated_simple = calculate_shannon_entropy(analyzer, empirical_probs)

        # Entropie théorique simulée
        simulated_theoretical = calculate_sequence_entropy_aep(analyzer, simulated_sequence)

        # T5 simulé avec la nouvelle méthode (fenêtre de 5)
        simulated_rate = if length(simulated_sequence) >= 5
            calculate_entropy_rate_new(analyzer, simulated_sequence)
        else
            current_metrics.entropy_rate
        end

        # Calculer les métriques simulées complètes pour obtenir StructEG et ConfEG
        simulated_evolution = calculate_all_metrics_evolution(analyzer, simulated_sequence, 4)
        simulated_metrics = isempty(simulated_evolution) ? current_metrics : simulated_evolution[end]

        # Calculer les différentiels absolus selon les définitions exactes en utilisant les fonctions dédiées
        diff_cond = calculate_diff_cond(simulated_conditional, current_metrics.conditional_entropy)        # DiffC = DiffCond
        diff_taux = calculate_diff_taux(simulated_rate, current_metrics.entropy_rate)                     # DiffT5 = DiffT5
        diff_div_entrop_g = calculate_diff_div_entrop_g(simulated_simple, current_metrics.simple_entropy)         # DiffDivEG = DiffDivEntropG
        diff_entrop_g = calculate_diff_entrop_g(simulated_theoretical, current_metrics.simple_entropy_theoretical)  # DiffEG = DiffEG
        diff_seg = calculate_diff_seg(simulated_metrics.struct_eg, current_metrics.struct_eg)             # DiffSEG
        diff_ceg = calculate_diff_ceg(simulated_metrics.conf_eg, current_metrics.conf_eg)                 # DiffCEG

        # Calcul du score composite selon la formule entropique optimale
        # SCORE = Mt5 × exp(-DiffEG) × exp(-DiffDivEG) × exp(-DiffC) / ((1 + DiffSEG + DiffCEG) × (1 + DiffT5))
        score = calculate_predictive_score(
            simulated_metrics.metric_entropy,   # Mt5
            diff_entrop_g,                      # DiffEG
            diff_div_entrop_g,                  # DiffDivEG
            diff_cond,                          # DiffC
            diff_seg,                           # DiffSEG
            diff_ceg,                           # DiffCEG
            diff_taux                           # DiffT5
        )

        predictive_differentials[possible_index5] = PredictiveDifferentials{T}(
            diff_cond, diff_taux, diff_div_entrop_g, diff_entrop_g, zero(T), diff_seg, diff_ceg, score
        )
    end

    return predictive_differentials
end

"""
    predict_next_index5(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> Union{String, Nothing}

Prédit la prochaine valeur INDEX5 basée sur le score composite le plus élevé.
"""
function predict_next_index5(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return nothing
    end

    position = length(sequence)
    predictive_diffs = calculate_predictive_differentials(analyzer, sequence, position)

    if isempty(predictive_diffs)
        return nothing
    end

    # Trouver la valeur avec le score le plus élevé
    best_index5 = nothing
    best_score = T(-Inf)

    for (index5, diff) in predictive_diffs
        if diff.score > best_score
            best_score = diff.score
            best_index5 = index5
        end
    end

    return best_index5
end

# ═══════════════════════════════════════════════════════════════════════════════
# AFFICHAGE ET RAPPORTS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    generate_metrics_table_report(results::Dict) -> String

Génère un rapport contenant le NOUVEAU tableau avec les 24 formules d'entropie.
"""
function generate_metrics_table_report(results::Dict)
    if haskey(results, "error")
        return "❌ Erreur: $(results["error"])"
    end

    # NOUVEAU TABLEAU AVEC LES 24 FORMULES - TOUTES LES MAINS
    sequence = results["sequence"]

    # Générer le nouveau tableau avec les 24 formules pour TOUTES les mains
    nouveau_tableau = generer_nouveau_tableau_24_formules(sequence, -1)

    return nouveau_tableau
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION ANCIEN TABLEAU (DÉSACTIVÉE)
# ═══════════════════════════════════════════════════════════════════════════════
#
# function generate_metrics_table_report_OLD_DISABLED(results::Dict)
#     if haskey(results, "error")
#         return "❌ Erreur: $(results["error"])"
#     end
#
#     report = IOBuffer()
#
#     # ANCIEN EN-TÊTE (DÉSACTIVÉ)
#     println(report, "📊 MÉTRIQUES ET DIFFÉRENTIELS POUR TOUTES LES MAINS:")
#     println(report, @sprintf("%-6s %10s %10s %10s    %-15s %10s %10s %12s %10s",
#             "Main", "DiffEG", "DiffSEG", "DiffCEG", "INDEX5_Observé", "Mt5", "DiffT5", "DiffDivEG", "DiffC"))
#     println(report, "-"^119)
#
#     # ANCIENNES DONNÉES (DÉSACTIVÉES)
#     differentials = results["differentials"]
#     entropy_evolution = results["entropy_evolution"]
#     sequence = results["sequence"]
#
#     for i in 1:length(differentials)
#         diff = differentials[i]
#         metrics = entropy_evolution[i]
#         observed_index5 = i <= length(sequence) ? sequence[i] : "N/A"
#
#         @printf(report, "%-6d %10.4f %10.4f %10.4f    %-15s %10.4f %10.4f %12.4f %10.4f\n",
#                i,                                        # Main
#                diff.diff_entrop_g,                       # DiffEG (différentiel)
#                diff.diff_seg,                            # DiffSEG (différentiel)
#                diff.diff_ceg,                            # DiffCEG (différentiel)
#                observed_index5,                          # INDEX5_Observé
#                metrics.metric_entropy,                    # Mt5
#                diff.diff_taux,                           # DiffT5
#                diff.diff_div_entrop_g,                   # DiffDivEG (différentiel)
#                diff.diff_cond)                           # DiffC
#     end
#
#     println(report, "-"^119)
#
#     return String(take!(report))
# end

"""
    display_analysis_results(results::Dict)

Affiche les résultats d'analyse de manière formatée.
"""
function display_analysis_results(results::Dict)
    if haskey(results, "error")
        @error "❌ $(results["error"])"
        return
    end

    println("\n" * "="^80)
    println("📊 RÉSULTATS D'ANALYSE ENTROPIQUE")
    println("="^80)

    if haskey(results, "game_id") && !isnothing(results["game_id"])
        println("🎮 Partie: $(results["game_id"])")
    end

    println("📏 Longueur de séquence: $(results["sequence_length"])")
    println("🔢 Valeurs uniques: $(results["unique_values_count"])")

    summary = results["analysis_summary"]
    println("\n📈 MÉTRIQUES FINALES:")
    @printf("   • Entropie Shannon (observée)  : %.6f bits\n", summary["final_shannon_entropy"])
    @printf("   • Entropie AEP (théorique)     : %.6f bits\n", summary["final_theoretical_entropy"])
    @printf("   • Entropie conditionnelle      : %.6f bits\n", summary["final_conditional_entropy"])
    @printf("   • Entropie Mt5                 : %.6f bits\n", summary["final_metric_entropy"])
    @printf("   • T5                           : %.6f bits\n", summary["entropy_rate"])

    # NOUVEAU TABLEAU AVEC LES 24 FORMULES D'ENTROPIE - TOUTES LES MAINS
    sequence = results["sequence"]
    if length(sequence) > 1
        # Afficher le nouveau tableau avec les 24 formules pour TOUTES les mains
        nouveau_tableau = generer_nouveau_tableau_24_formules(sequence, -1)
        println(nouveau_tableau)
    end

# ═══════════════════════════════════════════════════════════════════════════════
# ANCIEN CODE D'AFFICHAGE (DÉSACTIVÉ)
# ═══════════════════════════════════════════════════════════════════════════════
#
#     # ANCIEN AFFICHAGE (DÉSACTIVÉ)
#     differentials = results["differentials"]
#     entropy_evolution = results["entropy_evolution"]
#     sequence = results["sequence"]
#     if length(differentials) > 1
#         println("\n📊 MÉTRIQUES ET DIFFÉRENTIELS POUR TOUTES LES MAINS:")
#         println(@sprintf("%-6s %10s %10s %10s    %-15s %10s %10s %12s %10s",
#                 "Main", "DiffEG", "DiffSEG", "DiffCEG", "INDEX5_Observé", "Mt5", "DiffT5", "DiffDivEG", "DiffC"))
#         println("-"^119)
#
#         for i in 1:length(differentials)
#             diff = differentials[i]
#             metrics = entropy_evolution[i]
#             # Afficher l'INDEX5 de la main courante (Julia 1-based)
#             observed_index5 = sequence[i]  # sequence[i] correspond à la main i
#             score_str = isinf(diff.score) ? "∞" : @sprintf("%.4f", diff.score)
#
#             @printf("%-6d %10.4f %10.4f %10.4f    %-15s %10.4f %10.4f %12.4f %10.4f\n",
#                    i,                                        # Main
#                    diff.diff_entrop_g,                       # DiffEG (différentiel)
#                    diff.diff_seg,                            # DiffSEG (différentiel)
#                    diff.diff_ceg,                            # DiffCEG (différentiel)
#                    observed_index5,                          # INDEX5_Observé
#                    metrics.metric_entropy,                    # Mt5
#                    diff.diff_taux,                           # DiffT5
#                    diff.diff_div_entrop_g,                   # DiffDivEG (différentiel)
#                    diff.diff_cond)                           # DiffC
#         end
#         println("-"^119)
#     end

    println("="^80)
end



# ═══════════════════════════════════════════════════════════════════════════════
# INTERFACE PRINCIPALE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main()

Point d'entrée principal du programme avec interface interactive.
"""
function main()
    println("🎰 ANALYSEUR D'ENTROPIE BACCARAT - INDEX5 (Julia)")
    println("=" * "="^60)
    println("Basé sur les formules d'entropie de Shannon")
    println("Implémenté selon les bonnes pratiques Julia")
    println()

    # Initialisation de l'analyseur
    analyzer = EntropyAnalyzer{Float64}()

    # Chemin par défaut du fichier JSON
    default_filepath = "partie/dataset_baccarat_lupasco_20250704_092825_condensed.json"

    while true
        println("\n🎯 MENU PRINCIPAL:")
        println("1. Analyser une partie unique")
        println("2. Analyser plusieurs parties")
        println("3. Prédiction INDEX5 interactive")
        println("4. Afficher les probabilités théoriques")
        println("5. Tester les 24 formules (OBS + THEO)")
        println("6. Quitter")

        print("\nVotre choix (1-6): ")
        choix = readline()

        if choix == "1"
            analyze_single_game_interactive(analyzer, default_filepath)
        elseif choix == "2"
            analyze_multiple_games_interactive(analyzer, default_filepath)
        elseif choix == "3"
            interactive_prediction(analyzer)
        elseif choix == "4"
            display_theoretical_probabilities(analyzer)
        elseif choix == "5"
            tester_24_formules_interactive(default_filepath)
        elseif choix == "6"
            println("👋 Au revoir! Merci d'avoir utilisé l'analyseur d'entropie Julia!")
            break
        else
            println("❌ Choix invalide!")
        end
    end
end

"""
    analyze_single_game_interactive(analyzer::EntropyAnalyzer, filepath::String)

Interface interactive pour analyser une partie unique.
"""
function analyze_single_game_interactive(analyzer::EntropyAnalyzer, filepath::String)
    println("\n📁 Chargement des données...")
    parties = load_baccarat_data(filepath)

    if isempty(parties)
        return
    end

    println("📊 $(length(parties)) parties disponibles")
    print("Numéro de la partie à analyser (1-$(length(parties))): ")

    try
        partie_num = parse(Int, readline())
        if partie_num < 1 || partie_num > length(parties)
            println("❌ Numéro de partie invalide!")
            return
        end

        game_data = parties[partie_num]
        game_id = "Partie_$partie_num"

        println("\n🔄 Analyse en cours...")
        results = analyze_single_game(analyzer, game_data, game_id)

        display_analysis_results(results)

        # Génération automatique du rapport txt avec uniquement le tableau
        if !haskey(results, "error")
            rapport_filename = "partie$(partie_num).txt"
            rapport_content = generate_metrics_table_report(results)

            try
                open(rapport_filename, "w") do file
                    write(file, rapport_content)
                end
                println("\n📄 Rapport automatiquement généré : $rapport_filename")
            catch e
                println("\n❌ Erreur lors de la génération du rapport : $e")
            end
        end

        # Proposer une prédiction
        if !haskey(results, "error")
            sequence = results["sequence"]
            prediction = predict_next_index5(analyzer, sequence)

            if !isnothing(prediction)
                println("\n🔮 PRÉDICTION PROCHAINE VALEUR: $prediction")
            end
        end

    catch e
        println("❌ Erreur: $e")
    end
end

"""
    analyze_multiple_games_interactive(analyzer::EntropyAnalyzer, filepath::String)

Interface interactive pour analyser plusieurs parties.
"""
function analyze_multiple_games_interactive(analyzer::EntropyAnalyzer, filepath::String)
    println("\n📁 Chargement des données...")
    parties = load_baccarat_data(filepath)

    if isempty(parties)
        return
    end

    println("📊 $(length(parties)) parties disponibles")
    print("Nombre de parties à analyser (max $(length(parties))): ")

    try
        num_parties = parse(Int, readline())
        if num_parties < 1 || num_parties > length(parties)
            println("❌ Nombre invalide!")
            return
        end

        println("\n🔄 Analyse de $num_parties parties en cours...")

        all_entropies = Float64[]
        all_lengths = Int[]
        successful_analyses = 0

        for i in 1:num_parties
            game_data = parties[i]
            results = analyze_single_game(analyzer, game_data, "Partie_$i")

            if !haskey(results, "error")
                push!(all_entropies, results["analysis_summary"]["final_shannon_entropy"])
                push!(all_lengths, results["sequence_length"])
                successful_analyses += 1
            end

            if i % 10 == 0
                println("   Analysé: $i/$num_parties parties")
            end
        end

        # Statistiques globales
        println("\n" * "="^80)
        println("📈 STATISTIQUES GLOBALES ($successful_analyses parties analysées)")
        println("="^80)

        if !isempty(all_entropies)
            @printf("🎯 Entropie moyenne        : %.6f ± %.6f bits\n", mean(all_entropies), std(all_entropies))
            @printf("📏 Longueur moyenne        : %.2f ± %.2f mains\n", mean(all_lengths), std(all_lengths))
            @printf("📊 Entropie min/max        : %.6f / %.6f bits\n", minimum(all_entropies), maximum(all_entropies))
            @printf("📏 Longueur min/max        : %d / %d mains\n", minimum(all_lengths), maximum(all_lengths))
        end

        println("="^80)

    catch e
        println("❌ Erreur: $e")
    end
end

"""
    interactive_prediction(analyzer::EntropyAnalyzer)

Interface interactive pour les prédictions INDEX5.
"""
function interactive_prediction(analyzer::EntropyAnalyzer)
    println("\n🔮 PRÉDICTION INDEX5 INTERACTIVE")
    println("="^50)
    println("Entrez une séquence INDEX5 (format: 0_A_BANKER,1_C_TIE,...)")
    println("Ou tapez 'quit' pour revenir au menu principal")

    while true
        print("\nSéquence INDEX5: ")
        input = strip(readline())

        if lowercase(input) == "quit"
            break
        end

        if isempty(input)
            println("❌ Séquence vide!")
            continue
        end

        # Parser la séquence
        try
            sequence = String.(strip.(split(input, ',')))

            # Valider le format INDEX5
            valid_sequence = true
            for value in sequence
                if !occursin(r"^[01]_[ABC]_(BANKER|PLAYER|TIE)$", value)
                    println("❌ Format invalide pour '$value'. Format attendu: INDEX1_INDEX2_INDEX3")
                    valid_sequence = false
                    break
                end
            end

            if !valid_sequence
                continue
            end

            println("\n📊 Séquence analysée: $(length(sequence)) valeurs")

            # Calculer la prédiction
            prediction = predict_next_index5(analyzer, sequence)

            if isnothing(prediction)
                println("❌ Impossible de calculer une prédiction")
                continue
            end

            println("🏆 PRÉDICTION: $prediction")

        catch e
            println("❌ Erreur lors du parsing: $e")
        end
    end
end

"""
    display_theoretical_probabilities(analyzer::EntropyAnalyzer)

Affiche les probabilités théoriques INDEX5.
"""
function display_theoretical_probabilities(analyzer::EntropyAnalyzer)
    println("\n📊 PROBABILITÉS THÉORIQUES INDEX5")
    println("="^60)
    println(@sprintf("%-15s %12s %8s", "INDEX5", "Probabilité", "Pourcentage"))
    println("-"^60)

    # Trier par probabilité décroissante
    sorted_probs = sort(collect(analyzer.theoretical_probs), by = x -> x[2], rev = true)

    total_prob = 0.0
    for (index5, prob) in sorted_probs
        println(@sprintf("%-15s %12.6f %8.4f%%", index5, prob, prob * 100))
        total_prob += prob
    end

    println("-"^60)
    println(@sprintf("%-15s %12.6f %8.4f%%", "TOTAL", total_prob, total_prob * 100))
    println("="^60)

    # Statistiques
    probs = collect(values(analyzer.theoretical_probs))
    println("\n📈 STATISTIQUES:")
    @printf("   • Moyenne     : %.6f\n", mean(probs))
    @printf("   • Écart-type  : %.6f\n", std(probs))
    @printf("   • Min/Max     : %.6f / %.6f\n", minimum(probs), maximum(probs))
    @printf("   • Entropie max: %.6f bits (distribution uniforme)\n", log2(length(probs)))

    # Calculer l'entropie de la distribution théorique
    theoretical_entropy = -sum(p * log2(p) for p in probs)
    @printf("   • Entropie réelle: %.6f bits\n", theoretical_entropy)
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION DÉDIÉE - FORMULES DÉSACTIVÉES
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section contient TOUTES les formules mathématiques du programme
# qui ont été temporairement désactivées selon les instructions.
#
# ORGANISATION DES FORMULES :
# 1. Formules utilitaires de base
# 2. Formules d'entropie de Shannon
# 3. Formules AEP (Asymptotic Equipartition Property)
# 4. Formules de calcul Mt5 (Kolmogorov-Sinai)
# 5. Formules de calcul T5 (Entropy Rate)
# 6. Formules d'entropie conditionnelle
# 7. Formules de conformité et structure entropique
# 8. Formules de différentiels entropiques
# 9. Formule de score composite prédictif
# 10. Formules de calcul de blocs d'entropie
#
# ═══════════════════════════════════════════════════════════════════════════════

# ─────────────────────────────────────────────────────────────────────────────
# 1. FORMULES UTILITAIRES DE BASE
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de safe_log :
# return log(x) / log(base)

# FORMULE ORIGINALE de validate_probabilities :
# total = sum(clean_probs)
# if total ≈ zero(T)
#     throw(ArgumentError("All probabilities are zero"))
# end
# return clean_probs ./ total

# ─────────────────────────────────────────────────────────────────────────────
# 2. FORMULES D'ENTROPIE DE SHANNON
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_shannon_entropy :
# entropy = zero(T)
# for p in probs
#     if p > zero(T)
#         entropy -= p * safe_log(p, analyzer.base, analyzer.epsilon)
#     end
# end
# return entropy

# ─────────────────────────────────────────────────────────────────────────────
# 3. FORMULES AEP (ASYMPTOTIC EQUIPARTITION PROPERTY)
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_sequence_entropy_aep :
# total_log_prob = zero(T)
# for value in sequence
#     p_theo = get(analyzer.theoretical_probs, value, zero(T))
#     total_log_prob += safe_log(p_theo, analyzer.base, analyzer.epsilon)
# end
# return -total_log_prob / length(sequence)

# FORMULE ORIGINALE de calculate_sequence_entropy_aep_observed :
# counts = Dict{String, Int}()
# for value in sequence
#     counts[value] = get(counts, value, 0) + 1
# end
# n = length(sequence)
# total_log_prob = zero(T)
# for value in sequence
#     p_obs = T(counts[value]) / T(n)  # Probabilité observée
#     total_log_prob += safe_log(p_obs, analyzer.base, analyzer.epsilon)
# end
# return -total_log_prob / n

# ─────────────────────────────────────────────────────────────────────────────
# 4. FORMULES DE CALCUL Mt5 (KOLMOGOROV-SINAI)
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_metric_from_t5 :
# log2_18 = log(T(18)) / log(analyzer.base)  # log₂(18) calculé exactement
# if log2_18 ≈ zero(T)
#     return zero(T)
# end
# return t5_value / log2_18

# FORMULE ORIGINALE de calculate_metric_entropy_new :
# t5 = calculate_entropy_rate_new(analyzer, sequence)
# return calculate_metric_from_t5(analyzer, t5)

# FORMULE ORIGINALE de estimate_metric_entropy :
# max_length = length(block_entropies)
# return block_entropies[end] / max_length

# ─────────────────────────────────────────────────────────────────────────────
# 5. FORMULES DE CALCUL T5 (ENTROPY RATE)
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_entropy_rate_new :
# window_5 = sequence[(end-4):end]
# h5 = calculate_sequence_entropy_aep(analyzer, window_5)
# return h5

# FORMULE ORIGINALE de calculate_entropy_rate :
# return isempty(block_entropies) ? zero(T) : block_entropies[end]

# ─────────────────────────────────────────────────────────────────────────────
# 6. FORMULES D'ENTROPIE CONDITIONNELLE
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_conditional_entropy :
# context_transitions = Dict{String, Dict{String, Int}}()
# for i in 1:(length(sequence)-1)
#     context = sequence[i]
#     next_symbol = sequence[i+1]
#     if !haskey(context_transitions, context)
#         context_transitions[context] = Dict{String, Int}()
#     end
#     context_transitions[context][next_symbol] =
#         get(context_transitions[context], next_symbol, 0) + 1
# end
# total_transitions = sum(sum(values(transitions)) for transitions in values(context_transitions))
# conditional_entropy = zero(T)
# for (context, transitions) in context_transitions
#     context_prob = T(sum(values(transitions))) / T(total_transitions)
#     context_sequence = String[]
#     for (next_symbol, count) in transitions
#         append!(context_sequence, fill(next_symbol, count))
#     end
#     context_entropy = calculate_sequence_entropy_aep(analyzer, context_sequence)
#     conditional_entropy += context_prob * context_entropy
# end
# return conditional_entropy

# ─────────────────────────────────────────────────────────────────────────────
# 7. FORMULES DE CONFORMITÉ ET STRUCTURE ENTROPIQUE
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_conf_eg :
# return abs(entrop_g - eg_obs)

# FORMULE ORIGINALE de calculate_struct_eg :
# if eg_obs ≈ zero(T)
#     return T(Inf)  # Division par zéro -> infini
# end
# return entrop_g / eg_obs

# ─────────────────────────────────────────────────────────────────────────────
# 8. FORMULES DE DIFFÉRENTIELS ENTROPIQUES
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_diff_cond :
# return abs(current_conditional - previous_conditional)

# FORMULE ORIGINALE de calculate_diff_taux :
# return abs(current_rate - previous_rate)

# FORMULE ORIGINALE de calculate_diff_div_entrop_g :
# return abs(current_simple - previous_simple)

# FORMULE ORIGINALE de calculate_diff_entrop_g :
# return abs(current_theoretical - previous_theoretical)

# FORMULE ORIGINALE de calculate_diff_egobs :
# return abs(current_egobs - previous_egobs)

# FORMULE ORIGINALE de calculate_diff_seg :
# return abs(current_struct_eg - previous_struct_eg)

# FORMULE ORIGINALE de calculate_diff_ceg :
# return abs(current_conf_eg - previous_conf_eg)

# ─────────────────────────────────────────────────────────────────────────────
# 9. FORMULE DE SCORE COMPOSITE PRÉDICTIF
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_predictive_score :
# exp_diff_eg = exp(-diff_eg)
# exp_diff_div_eg = exp(-diff_div_eg)
# exp_diff_c = exp(-diff_c)
# denominator = (one(T) + diff_seg + diff_ceg) * (one(T) + diff_t5)
# if denominator ≈ zero(T)
#     return T(Inf)  # Score infini
# else
#     numerator = mt5 * exp_diff_eg * exp_diff_div_eg * exp_diff_c
#     return numerator / denominator
# end

# ─────────────────────────────────────────────────────────────────────────────
# 10. FORMULES DE CALCUL DE BLOCS D'ENTROPIE
# ─────────────────────────────────────────────────────────────────────────────

# FORMULE ORIGINALE de calculate_block_entropies :
# for block_len in 1:min(max_length, length(sequence))
#     if block_len == 1
#         block_entropy = calculate_sequence_entropy_aep(analyzer, sequence)
#     else
#         total_entropy = zero(T)
#         num_blocks = 0
#         for i in 1:(length(sequence) - block_len + 1)
#             block_sequence = sequence[i:(i + block_len - 1)]
#             total_entropy += calculate_sequence_entropy_aep(analyzer, block_sequence)
#             num_blocks += 1
#         end
#         block_entropy = num_blocks > 0 ? total_entropy / num_blocks : zero(T)
#     end
#     push!(entropies, block_entropy)
# end

# ═══════════════════════════════════════════════════════════════════════════════
# FIN DE LA SECTION DÉDIÉE - FORMULES DÉSACTIVÉES
# ═══════════════════════════════════════════════════════════════════════════════
#
# RÉSUMÉ DES FORMULES DÉSACTIVÉES :
# ✅ 10 catégories de formules mathématiques identifiées et désactivées
# ✅ Toutes les formules conservées dans cette section dédiée
# ✅ Valeurs par défaut temporaires assignées dans les fonctions
# ✅ Code syntaxiquement correct maintenu
# ✅ Documentation complète des formules originales
#
# TOTAL : Toutes les formules du programme regroupées et désactivées
# ═══════════════════════════════════════════════════════════════════════════════

# ─────────────────────────────────────────────────────────────────────────────
# ANCIEN TABLEAU DÉSACTIVÉ
# ─────────────────────────────────────────────────────────────────────────────
#
# ANCIEN FORMAT DE TABLEAU (DÉSACTIVÉ) :
# Main       DiffEG    DiffSEG    DiffCEG    INDEX5_Observé         Mt5     DiffT5    DiffDivEG      DiffC
#
# ANCIEN CODE D'AFFICHAGE (DÉSACTIVÉ) :
# println(@sprintf("%-6s %10s %10s %10s    %-15s %10s %10s %12s %10s",
#         "Main", "DiffEG", "DiffSEG", "DiffCEG", "INDEX5_Observé", "Mt5", "DiffT5", "DiffDivEG", "DiffC"))
# println("-"^119)
#
# @printf("%-6d %10.4f %10.4f %10.4f    %-15s %10.4f %10.4f %12.4f %10.4f\n",
#        i,                                        # Main
#        diff.diff_entrop_g,                       # DiffEG (différentiel)
#        diff.diff_seg,                            # DiffSEG (différentiel)
#        diff.diff_ceg,                            # DiffCEG (différentiel)
#        observed_index5,                          # INDEX5_Observé
#        metrics.metric_entropy,                   # Mt5
#        diff.diff_taux,                           # DiffT5
#        diff.diff_div_entrop_g,                   # DiffDivEG (différentiel)
#        diff.diff_cond)                           # DiffC
#
# ─────────────────────────────────────────────────────────────────────────────

# ═══════════════════════════════════════════════════════════════════════════════
# POINT D'ENTRÉE
# ═══════════════════════════════════════════════════════════════════════════════

# Exécuter le programme principal si ce fichier est exécuté directement
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end
