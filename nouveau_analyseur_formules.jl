"""
ANALYSEUR D'ENTROPIE BACCARAT - INDEX5 (Julia Version)
=====================================================

Programme d'analyse de l'évolution de l'entropie pour l'INDEX5 au cours d'une partie de baccarat.
Basé sur les formules d'entropie de Shannon et implémenté selon les bonnes pratiques Julia.

Architecture modulaire avec types paramétriques et dispatch multiple.
"""

using JSON
using Statistics
using LinearAlgebra
using Printf

# ═══════════════════════════════════════════════════════════════════════════════
# TYPES ET STRUCTURES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    EntropyAnalyzer{T<:AbstractFloat}

Analyseur d'entropie pour le baccarat basé sur les formules de Shannon.

# Fields
- `base::T`: Base pour les calculs logarithmiques (défaut: 2.0 pour bits)
- `epsilon::T`: Petite valeur pour éviter log(0) (défaut: 1e-12)
- `theoretical_probs::Dict{String,T}`: Probabilités théoriques INDEX5

# Examples
```julia
analyzer = EntropyAnalyzer{Float64}()
entropy = calculate_shannon_entropy(analyzer, [0.5, 0.5])
```
"""
struct EntropyAnalyzer{T<:AbstractFloat}
    base::T
    epsilon::T
    theoretical_probs::Dict{String,T}

    function EntropyAnalyzer{T}(
        base::T = T(2.0),
        epsilon::T = T(1e-12)
    ) where T<:AbstractFloat
        if base <= 1
            throw(ArgumentError("Base must be > 1, got $base"))
        end
        if epsilon <= 0
            throw(ArgumentError("Epsilon must be > 0, got $epsilon"))
        end

        # Probabilités théoriques INDEX5 normalisées
        theoretical_probs = Dict{String,T}(
            "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
            "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
            "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
            "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
            "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
            "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
            "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
            "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
            "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
        )

        new{T}(base, epsilon, theoretical_probs)
    end
end

# Constructeur de convenance
EntropyAnalyzer(args...) = EntropyAnalyzer{Float64}(args...)

"""
    EntropyMetrics{T<:AbstractFloat}

Structure pour stocker les métriques d'entropie calculées.
"""
struct EntropyMetrics{T<:AbstractFloat}
    position::Int
    sequence_length::Int
    unique_values::Int
    simple_entropy::T
    simple_entropy_theoretical::T
    conditional_entropy::T
    metric_entropy::T
    entropy_rate::T
    entropy_aep_observed::T        # EGobs (Entropie Générale Observée)
    conf_eg::T                     # ConfEG = |EntropG - EGobs|
    struct_eg::T                   # StructEG = EntropG/EGobs
    block_entropies::Vector{T}
end

"""
    PredictiveDifferentials{T<:AbstractFloat}

Structure pour les différentiels prédictifs selon les définitions exactes.
Correspondances:
- DiffC = DiffCond (Différentiel Entropie Conditionnelle)
- DiffT5 = DiffT5 (Différentiel T5)
- DiffDivEG = DiffDivEntropG (Différentiel Diversité Entropique)
- DiffEG = DiffEG (Différentiel Entropie Générale)
- DiffEGobs = DiffEGobs (Différentiel Entropie Générale Observée)
- DiffSEG = DiffSEG (Différentiel Structure Entropique)
- DiffCEG = DiffCEG (Différentiel Conformité Entropique)
- SCORE = (DiffC + DiffEG) / (DiffT5 + DiffDivEG)
"""
struct PredictiveDifferentials{T<:AbstractFloat}
    diff_cond::T          # DiffC = DiffCond
    diff_taux::T          # DiffT5 = DiffT5
    diff_div_entrop_g::T  # DiffDivEG = DiffDivEntropG
    diff_entrop_g::T      # DiffEG = DiffEG
    diff_egobs::T         # DiffEGobs = DiffEGobs
    diff_seg::T           # DiffSEG = DiffSEG
    diff_ceg::T           # DiffCEG = DiffCEG
    score::T              # SCORE = (DiffC + DiffEG) / (DiffT5 + DiffDivEG)
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS UTILITAIRES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    safe_log(x::T, base::T, epsilon::T) where T<:AbstractFloat -> T

Calcul sécurisé du logarithme avec gestion de log(0).
"""
function safe_log(x::T, base::T, epsilon::T) where T<:AbstractFloat
    if x <= zero(T)
        x = epsilon
    end
    return log(x) / log(base)
end

"""
    validate_probabilities(probs::Vector{T}) where T<:AbstractFloat -> Vector{T}

Valide et normalise un vecteur de probabilités.
"""
function validate_probabilities(probs::Vector{T}) where T<:AbstractFloat
    if isempty(probs)
        throw(ArgumentError("Probability vector cannot be empty"))
    end

    # Convertir en Float64 si nécessaire et filtrer les valeurs négatives
    clean_probs = max.(probs, zero(T))

    # Normaliser
    total = sum(clean_probs)
    if total ≈ zero(T)
        throw(ArgumentError("All probabilities are zero"))
    end

    return clean_probs ./ total
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION DÉDIÉE - TOUTES LES FORMULES MATHÉMATIQUES DE FORMULES1.TXT
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section regroupe TOUTES les 24 formules mathématiques selon Formules1.txt
# avec leurs méthodes de calcul selon les définitions exactes
#
# ORGANISATION COMPLÈTE DES FORMULES :
#
# 📊 FORMULES FONDAMENTALES (10 fonctions : 5 formules × 2 versions) :
# 1A/1B. Entropie de Shannon jointe (observée/théorique)
# 2A/2B. Entropie AEP (Asymptotic Equipartition Property)
# 3A/3B. Taux d'entropie (Entropy Rate)
# 4A/4B. Entropie métrique (Kolmogorov-Sinai)
# 5A/5B. Entropie conditionnelle cumulative
#
# 🔬 FORMULES AVANCÉES (8 fonctions : 4 formules × 2 versions) :
# 6A/6B. Divergence KL (Entropie Relative)
# 7A/7B. Information mutuelle
# 8A/8B. Entropie croisée
# 9A/9B. Entropie topologique
#
# 📐 FORMULES SPÉCIALISÉES (6 fonctions : 3 formules × 2 versions) :
# 10A/10B. Entropie block cumulative
# 11A/11B. Entropie conditionnelle décroissante
# 12A/12B. Théorème AEP (Shannon-McMillan-Breiman)
#
# TOTAL : 24 formules complètes avec toutes leurs méthodes de calcul
# ═══════════════════════════════════════════════════════════════════════════════

# ─────────────────────────────────────────────────────────────────────────────
# FORMULES FONDAMENTALES (1-5) - 10 FONCTIONS
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculer_formule1A_shannon_jointe_obs(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 1A : H_obs(X₁, X₂, ..., Xₙ) = -∑ p_obs(x₁,...,xₙ) log₂ p_obs(x₁,...,xₙ)
Entropie de Shannon jointe avec probabilités observées.
"""
function calculer_formule1A_shannon_jointe_obs(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer les probabilités observées
    counts = Dict{String, Int}()
    for value in sequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie de Shannon
    entropy = zero(T)
    n = length(sequence)
    for (_, count) in counts
        p_obs = T(count) / T(n)
        if p_obs > zero(T)
            entropy -= p_obs * safe_log(p_obs, analyzer.base, analyzer.epsilon)
        end
    end

    return entropy
end

"""
    calculer_formule1B_shannon_jointe_theo(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 1B : H_theo(X₁, X₂, ..., Xₙ) = -∑ p_theo(x₁,...,xₙ) log₂ p_theo(x₁,...,xₙ)
Entropie de Shannon jointe avec probabilités théoriques INDEX5.
"""
function calculer_formule1B_shannon_jointe_theo(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Utiliser les probabilités théoriques INDEX5
    entropy = zero(T)
    for (value, prob) in analyzer.theoretical_probs
        if prob > zero(T)
            entropy -= prob * safe_log(prob, analyzer.base, analyzer.epsilon)
        end
    end

    return entropy
end

"""
    calculer_formule2A_aep_obs(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 2A : H_AEP_obs(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_obs(xᵢ)
Entropie AEP avec probabilités observées.
"""
function calculer_formule2A_aep_obs(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer les probabilités observées
    counts = Dict{String, Int}()
    for value in sequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer la somme des logarithmes
    total_log_prob = zero(T)
    n = length(sequence)
    for value in sequence
        p_obs = T(get(counts, value, 0)) / T(n)
        total_log_prob += safe_log(p_obs, analyzer.base, analyzer.epsilon)
    end

    return -total_log_prob / n
end

"""
    calculer_formule2B_aep_theo(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 2B : H_AEP_theo(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_theo(xᵢ)
Entropie AEP avec probabilités théoriques INDEX5.
"""
function calculer_formule2B_aep_theo(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer la somme des logarithmes avec probabilités théoriques
    total_log_prob = zero(T)
    for value in sequence
        p_theo = get(analyzer.theoretical_probs, value, zero(T))
        total_log_prob += safe_log(p_theo, analyzer.base, analyzer.epsilon)
    end

    return -total_log_prob / length(sequence)
end

"""
    calculer_formule3A_taux_entropie_obs(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 3A : H_obs(Ξ) = lim_{n→∞} (1/n) H_obs(X₁, X₂, ..., Xₙ)
Taux d'entropie avec probabilités observées.
"""
function calculer_formule3A_taux_entropie_obs(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Approximation : utiliser l'entropie jointe divisée par la longueur
    joint_entropy = calculer_formule1A_shannon_jointe_obs(analyzer, sequence)
    return joint_entropy / length(sequence)
end

"""
    calculer_formule3B_taux_entropie_theo(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 3B : H_theo(Ξ) = lim_{n→∞} (1/n) H_theo(X₁, X₂, ..., Xₙ)
Taux d'entropie avec probabilités théoriques INDEX5.
"""
function calculer_formule3B_taux_entropie_theo(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Approximation : utiliser l'entropie jointe théorique divisée par la longueur
    joint_entropy = calculer_formule1B_shannon_jointe_theo(analyzer, sequence)
    return joint_entropy / length(sequence)
end

"""
    calculer_formule4A_entropie_metrique_obs(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 4A : h_μ_obs(T) = sup{h_μ_obs(T, α) : α partition finie}
Entropie métrique (Kolmogorov-Sinai) avec mesure observée.
"""
function calculer_formule4A_entropie_metrique_obs(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Approximation pratique : utiliser le taux d'entropie observé
    return calculer_formule3A_taux_entropie_obs(analyzer, sequence)
end

"""
    calculer_formule4B_entropie_metrique_theo(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 4B : h_μ_theo(T) = sup{h_μ_theo(T, α) : α partition finie}
Entropie métrique (Kolmogorov-Sinai) avec mesure théorique INDEX5.
"""
function calculer_formule4B_entropie_metrique_theo(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Approximation pratique : utiliser le taux d'entropie théorique
    return calculer_formule3B_taux_entropie_theo(analyzer, sequence)
end

"""
    calculer_formule5A_conditionnelle_obs(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 5A : H_obs(Xₙ|X₁, ..., Xₙ₋₁) = H_obs(X₁, ..., Xₙ) - H_obs(X₁, ..., Xₙ₋₁)
Entropie conditionnelle cumulative avec probabilités observées.
"""
function calculer_formule5A_conditionnelle_obs(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if length(sequence) < 2
        return zero(T)
    end

    # H_obs(X₁, ..., Xₙ)
    h_n = calculer_formule1A_shannon_jointe_obs(analyzer, sequence)

    # H_obs(X₁, ..., Xₙ₋₁)
    h_n_minus_1 = calculer_formule1A_shannon_jointe_obs(analyzer, sequence[1:end-1])

    return h_n - h_n_minus_1
end

"""
    calculer_formule5B_conditionnelle_theo(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 5B : H_theo(Xₙ|X₁, ..., Xₙ₋₁) = H_theo(X₁, ..., Xₙ) - H_theo(X₁, ..., Xₙ₋₁)
Entropie conditionnelle cumulative avec probabilités théoriques INDEX5.
"""
function calculer_formule5B_conditionnelle_theo(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if length(sequence) < 2
        return zero(T)
    end

    # H_theo(X₁, ..., Xₙ)
    h_n = calculer_formule1B_shannon_jointe_theo(analyzer, sequence)

    # H_theo(X₁, ..., Xₙ₋₁)
    h_n_minus_1 = calculer_formule1B_shannon_jointe_theo(analyzer, sequence[1:end-1])

    return h_n - h_n_minus_1
end

# ─────────────────────────────────────────────────────────────────────────────
# FORMULES AVANCÉES (6-9) - 8 FONCTIONS
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculer_formule6A_divergence_kl_obs_theo(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 6A : D_KL_obs_theo(P_n||Q_n) = ∑ᵢ₌₁ⁿ p_obs(xᵢ) log₂(p_obs(xᵢ)/p_theo(xᵢ))
Divergence KL entre probabilités observées et théoriques.
"""
function calculer_formule6A_divergence_kl_obs_theo(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer les probabilités observées
    counts = Dict{String, Int}()
    for value in sequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer la divergence KL
    kl_div = zero(T)
    n = length(sequence)
    for (value, count) in counts
        p_obs = T(count) / T(n)
        p_theo = get(analyzer.theoretical_probs, value, analyzer.epsilon)
        if p_obs > zero(T) && p_theo > zero(T)
            kl_div += p_obs * safe_log(p_obs / p_theo, analyzer.base, analyzer.epsilon)
        end
    end

    return kl_div
end

"""
    calculer_formule6B_divergence_kl_theo_unif(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 6B : D_KL_theo_unif(P_n||U_n) = ∑ᵢ₌₁ⁿ p_theo(xᵢ) log₂(p_theo(xᵢ)/p_unif(xᵢ))
Divergence KL entre probabilités théoriques INDEX5 et distribution uniforme.
"""
function calculer_formule6B_divergence_kl_theo_unif(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    # Distribution uniforme sur 18 valeurs INDEX5
    p_unif = T(1.0 / 18.0)

    # Calculer la divergence KL
    kl_div = zero(T)
    for (_, p_theo) in analyzer.theoretical_probs
        if p_theo > zero(T)
            kl_div += p_theo * safe_log(p_theo / p_unif, analyzer.base, analyzer.epsilon)
        end
    end

    return kl_div
end

"""
    calculer_formule7A_information_mutuelle_obs(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 7A : I_obs(X₁ⁿ; Y₁ⁿ) = H_obs(X₁ⁿ) + H_obs(Y₁ⁿ) - H_obs(X₁ⁿ, Y₁ⁿ)
Information mutuelle avec probabilités observées.
"""
function calculer_formule7A_information_mutuelle_obs(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if length(sequence) < 2
        return zero(T)
    end

    # Diviser la séquence en deux parties pour simuler X et Y
    mid = length(sequence) ÷ 2
    seq_x = sequence[1:mid]
    seq_y = sequence[mid+1:end]

    # Calculer H_obs(X), H_obs(Y), H_obs(X,Y)
    h_x = calculer_formule1A_shannon_jointe_obs(analyzer, seq_x)
    h_y = calculer_formule1A_shannon_jointe_obs(analyzer, seq_y)
    h_xy = calculer_formule1A_shannon_jointe_obs(analyzer, sequence)

    return h_x + h_y - h_xy
end

"""
    calculer_formule7B_information_mutuelle_theo(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 7B : I_theo(X₁ⁿ; Y₁ⁿ) = H_theo(X₁ⁿ) + H_theo(Y₁ⁿ) - H_theo(X₁ⁿ, Y₁ⁿ)
Information mutuelle avec probabilités théoriques INDEX5.
"""
function calculer_formule7B_information_mutuelle_theo(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if length(sequence) < 2
        return zero(T)
    end

    # Diviser la séquence en deux parties pour simuler X et Y
    mid = length(sequence) ÷ 2
    seq_x = sequence[1:mid]
    seq_y = sequence[mid+1:end]

    # Calculer H_theo(X), H_theo(Y), H_theo(X,Y)
    h_x = calculer_formule1B_shannon_jointe_theo(analyzer, seq_x)
    h_y = calculer_formule1B_shannon_jointe_theo(analyzer, seq_y)
    h_xy = calculer_formule1B_shannon_jointe_theo(analyzer, sequence)

    return h_x + h_y - h_xy
end

"""
    calculer_formule8A_entropie_croisee_obs_theo(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 8A : H_cross_obs_theo(P_n, Q_n) = -∑ᵢ₌₁ⁿ p_obs(xᵢ) log₂ p_theo(xᵢ)
Entropie croisée entre probabilités observées et théoriques.
"""
function calculer_formule8A_entropie_croisee_obs_theo(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer les probabilités observées
    counts = Dict{String, Int}()
    for value in sequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie croisée
    cross_entropy = zero(T)
    n = length(sequence)
    for (value, count) in counts
        p_obs = T(count) / T(n)
        p_theo = get(analyzer.theoretical_probs, value, analyzer.epsilon)
        cross_entropy -= p_obs * safe_log(p_theo, analyzer.base, analyzer.epsilon)
    end

    return cross_entropy
end

"""
    calculer_formule8B_entropie_croisee_theo_unif(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 8B : H_cross_theo_unif(P_n, U_n) = -∑ᵢ₌₁ⁿ p_theo(xᵢ) log₂ p_unif(xᵢ)
Entropie croisée entre probabilités théoriques INDEX5 et distribution uniforme.
"""
function calculer_formule8B_entropie_croisee_theo_unif(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    # Distribution uniforme sur 18 valeurs INDEX5
    p_unif = T(1.0 / 18.0)

    # Calculer l'entropie croisée
    cross_entropy = zero(T)
    for (_, p_theo) in analyzer.theoretical_probs
        cross_entropy -= p_theo * safe_log(p_unif, analyzer.base, analyzer.epsilon)
    end

    return cross_entropy
end

"""
    calculer_formule9A_entropie_topologique_obs(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 9A : h_top_obs(f) = lim_{ε→0} lim_{n→∞} (1/n) log s_n_obs(ε)
Entropie topologique avec patterns observés.
"""
function calculer_formule9A_entropie_topologique_obs(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Approximation : compter les motifs distincts observés
    unique_patterns = Set(sequence)
    s_n = length(unique_patterns)

    return safe_log(T(s_n), analyzer.base, analyzer.epsilon) / length(sequence)
end

"""
    calculer_formule9B_entropie_topologique_theo(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 9B : h_top_theo(f) = lim_{ε→0} lim_{n→∞} (1/n) log s_n_theo(ε)
Entropie topologique avec modèle théorique INDEX5.
"""
function calculer_formule9B_entropie_topologique_theo(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Approximation : utiliser le nombre de valeurs INDEX5 théoriques possibles
    s_n_theo = length(analyzer.theoretical_probs)

    return safe_log(T(s_n_theo), analyzer.base, analyzer.epsilon) / length(sequence)
end

# ─────────────────────────────────────────────────────────────────────────────
# FORMULES SPÉCIALISÉES (10-12) - 6 FONCTIONS
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculer_formule10A_block_cumulative_obs(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 10A : H_block_obs(X₁ⁿ) = (1/n) ∑ₖ₌₁ⁿ H_obs(X₁ᵏ)
Entropie block cumulative avec probabilités observées.
"""
function calculer_formule10A_block_cumulative_obs(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer la somme des entropies pour chaque préfixe
    total_entropy = zero(T)
    for k in 1:length(sequence)
        prefix = sequence[1:k]
        total_entropy += calculer_formule1A_shannon_jointe_obs(analyzer, prefix)
    end

    return total_entropy / length(sequence)
end

"""
    calculer_formule10B_block_cumulative_theo(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 10B : H_block_theo(X₁ⁿ) = (1/n) ∑ₖ₌₁ⁿ H_theo(X₁ᵏ)
Entropie block cumulative avec probabilités théoriques INDEX5.
"""
function calculer_formule10B_block_cumulative_theo(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer la somme des entropies théoriques pour chaque préfixe
    total_entropy = zero(T)
    for k in 1:length(sequence)
        prefix = sequence[1:k]
        total_entropy += calculer_formule1B_shannon_jointe_theo(analyzer, prefix)
    end

    return total_entropy / length(sequence)
end

"""
    calculer_formule11A_conditionnelle_decroissante_obs(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 11A : H_dec_obs(X₁ⁿ) = ∑ₖ₌₁ⁿ (1/k) H_obs(Xₖ|X₁ᵏ⁻¹)
Entropie conditionnelle décroissante avec probabilités observées.
"""
function calculer_formule11A_conditionnelle_decroissante_obs(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer la somme pondérée des entropies conditionnelles
    total_weighted_entropy = zero(T)
    for k in 1:length(sequence)
        if k == 1
            # H_obs(X₁) = H_obs(X₁|∅)
            h_cond = calculer_formule1A_shannon_jointe_obs(analyzer, sequence[1:1])
        else
            # H_obs(Xₖ|X₁ᵏ⁻¹) approximé par différence d'entropies
            h_k = calculer_formule1A_shannon_jointe_obs(analyzer, sequence[1:k])
            h_k_minus_1 = calculer_formule1A_shannon_jointe_obs(analyzer, sequence[1:k-1])
            h_cond = h_k - h_k_minus_1
        end
        total_weighted_entropy += h_cond / k
    end

    return total_weighted_entropy
end

"""
    calculer_formule11B_conditionnelle_decroissante_theo(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 11B : H_dec_theo(X₁ⁿ) = ∑ₖ₌₁ⁿ (1/k) H_theo(Xₖ|X₁ᵏ⁻¹)
Entropie conditionnelle décroissante avec probabilités théoriques INDEX5.
"""
function calculer_formule11B_conditionnelle_decroissante_theo(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer la somme pondérée des entropies conditionnelles théoriques
    total_weighted_entropy = zero(T)
    for k in 1:length(sequence)
        if k == 1
            # H_theo(X₁) = H_theo(X₁|∅)
            h_cond = calculer_formule1B_shannon_jointe_theo(analyzer, sequence[1:1])
        else
            # H_theo(Xₖ|X₁ᵏ⁻¹) approximé par différence d'entropies
            h_k = calculer_formule1B_shannon_jointe_theo(analyzer, sequence[1:k])
            h_k_minus_1 = calculer_formule1B_shannon_jointe_theo(analyzer, sequence[1:k-1])
            h_cond = h_k - h_k_minus_1
        end
        total_weighted_entropy += h_cond / k
    end

    return total_weighted_entropy
end

"""
    calculer_formule12A_theoreme_aep_obs(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 12A : AEP_obs(X₁ⁿ) = -(1/n) log₂ ∏ᵢ₌₁ⁿ p_obs(xᵢ)
Théorème AEP (Shannon-McMillan-Breiman) avec probabilités observées.
"""
function calculer_formule12A_theoreme_aep_obs(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer les probabilités observées
    counts = Dict{String, Int}()
    for value in sequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer le produit des probabilités (en log pour éviter l'underflow)
    total_log_prob = zero(T)
    n = length(sequence)
    for value in sequence
        p_obs = T(get(counts, value, 0)) / T(n)
        total_log_prob += safe_log(p_obs, analyzer.base, analyzer.epsilon)
    end

    return -total_log_prob / n
end

"""
    calculer_formule12B_theoreme_aep_theo(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 12B : AEP_theo(X₁ⁿ) = -(1/n) log₂ ∏ᵢ₌₁ⁿ p_theo(xᵢ)
Théorème AEP (Shannon-McMillan-Breiman) avec probabilités théoriques INDEX5.
"""
function calculer_formule12B_theoreme_aep_theo(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer le produit des probabilités théoriques (en log pour éviter l'underflow)
    total_log_prob = zero(T)
    for value in sequence
        p_theo = get(analyzer.theoretical_probs, value, analyzer.epsilon)
        total_log_prob += safe_log(p_theo, analyzer.base, analyzer.epsilon)
    end

    return -total_log_prob / length(sequence)
end

# ═══════════════════════════════════════════════════════════════════════════════
# CHARGEMENT ET EXTRACTION DES DONNÉES (IDENTIQUE À ENTROPIE_BACCARAT_ANALYZER.JL)
# ═══════════════════════════════════════════════════════════════════════════════

"""
    load_baccarat_data(filepath::String) -> Vector{Dict}

Charge les données de baccarat depuis un fichier JSON.
Gère différentes structures JSON possibles.
"""
function load_baccarat_data(filepath::String)
    try
        data = JSON.parsefile(filepath)

        # Vérifier la structure du JSON
        if isa(data, Dict) && haskey(data, "parties_condensees")
            parties = data["parties_condensees"]
            @info "✅ Données chargées: $(length(parties)) parties trouvées"
            return parties
        elseif isa(data, Vector)
            @info "✅ Données chargées: $(length(data)) parties trouvées"
            return data
        else
            @warn "❌ Structure JSON non reconnue"
            return Dict[]
        end
    catch e
        if isa(e, SystemError)
            @error "❌ Erreur: Fichier $filepath non trouvé"
        else
            @error "❌ Erreur JSON: $e"
        end
        return Dict[]
    end
end

"""
    extract_index5_sequence(game_data::Dict) -> Vector{String}

Extrait la séquence INDEX5 d'une partie.
Filtre les mains d'ajustement et extrait uniquement les valeurs INDEX5 valides.
"""
function extract_index5_sequence(game_data::Dict)
    sequence = String[]

    # Vérifier différentes structures possibles
    if haskey(game_data, "hands")
        # Structure: {"hands": [...]}
        for hand in game_data["hands"]
            # Exclure les mains d'ajustement
            if (haskey(hand, "main_number") &&
                !isnothing(hand["main_number"]) &&
                haskey(hand, "INDEX5") &&
                !isnothing(hand["INDEX5"]) &&
                !isempty(strip(string(hand["INDEX5"]))))
                push!(sequence, string(hand["INDEX5"]))
            end
        end
    elseif haskey(game_data, "mains_condensees")
        # Structure: {"mains_condensees": [...]}
        for main in game_data["mains_condensees"]
            # Exclure les mains d'ajustement
            if (haskey(main, "main_number") &&
                !isnothing(main["main_number"]) &&
                haskey(main, "index5") &&
                !isnothing(main["index5"]) &&
                !isempty(strip(string(main["index5"]))))
                push!(sequence, string(main["index5"]))
            end
        end
    else
        @warn "❌ Structure de partie non reconnue. Clés disponibles: $(keys(game_data))"
        return String[]
    end

    @info "🔍 Séquence extraite: $(length(sequence)) mains valides"
    return sequence
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE DE CALCUL DES 24 FORMULES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculate_all_metrics_evolution(analyzer::EntropyAnalyzer{T}, sequence::Vector{String}) where T -> Vector{EntropyMetrics{T}}

Calcule l'évolution des métriques d'entropie position par position dans la séquence.
Remplace les 16 métriques originales par les 24 formules de Formules1.txt.
"""
function calculate_all_metrics_evolution(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return EntropyMetrics{T}[]
    end

    results = EntropyMetrics{T}[]

    # Calculer les métriques pour chaque position
    for n in 1:length(sequence)
        current_sequence = sequence[1:n]
        unique_vals = length(Set(current_sequence))

        # Calculer toutes les 24 formules (remplace les 8 métriques de base)
        f1a = calculer_formule1A_shannon_jointe_obs(analyzer, current_sequence)
        f1b = calculer_formule1B_shannon_jointe_theo(analyzer, current_sequence)
        f2a = calculer_formule2A_aep_obs(analyzer, current_sequence)
        f2b = calculer_formule2B_aep_theo(analyzer, current_sequence)
        f3a = calculer_formule3A_taux_entropie_obs(analyzer, current_sequence)
        f3b = calculer_formule3B_taux_entropie_theo(analyzer, current_sequence)
        f4a = calculer_formule4A_entropie_metrique_obs(analyzer, current_sequence)
        f4b = calculer_formule4B_entropie_metrique_theo(analyzer, current_sequence)
        f5a = calculer_formule5A_conditionnelle_obs(analyzer, current_sequence)
        f5b = calculer_formule5B_conditionnelle_theo(analyzer, current_sequence)

        # Formules avancées
        f6a = calculer_formule6A_divergence_kl_obs_theo(analyzer, current_sequence)
        f6b = calculer_formule6B_divergence_kl_theo_unif(analyzer, current_sequence)
        f7a = calculer_formule7A_information_mutuelle_obs(analyzer, current_sequence)
        f7b = calculer_formule7B_information_mutuelle_theo(analyzer, current_sequence)
        f8a = calculer_formule8A_entropie_croisee_obs_theo(analyzer, current_sequence)
        f8b = calculer_formule8B_entropie_croisee_theo_unif(analyzer, current_sequence)
        f9a = calculer_formule9A_entropie_topologique_obs(analyzer, current_sequence)
        f9b = calculer_formule9B_entropie_topologique_theo(analyzer, current_sequence)

        # Formules spécialisées
        f10a = calculer_formule10A_block_cumulative_obs(analyzer, current_sequence)
        f10b = calculer_formule10B_block_cumulative_theo(analyzer, current_sequence)
        f11a = calculer_formule11A_conditionnelle_decroissante_obs(analyzer, current_sequence)
        f11b = calculer_formule11B_conditionnelle_decroissante_theo(analyzer, current_sequence)
        f12a = calculer_formule12A_theoreme_aep_obs(analyzer, current_sequence)
        f12b = calculer_formule12B_theoreme_aep_theo(analyzer, current_sequence)

        # Adapter à la structure EntropyMetrics (utiliser les formules comme métriques de base)
        metrics = EntropyMetrics{T}(
            n,                    # position
            n,                    # sequence_length
            unique_vals,          # unique_values
            f1a,                  # simple_entropy (F1A)
            f1b,                  # simple_entropy_theoretical (F1B)
            f5a,                  # conditional_entropy (F5A)
            f4a,                  # metric_entropy (F4A)
            f3a,                  # entropy_rate (F3A)
            f2a,                  # entropy_aep_observed (F2A)
            abs(f1b - f2a),       # conf_eg = |EntropG - EGobs|
            f1b / max(f2a, analyzer.epsilon),  # struct_eg = EntropG/EGobs
            T[f10a, f10b, f11a, f11b, f12a, f12b]  # block_entropies (formules spécialisées)
        )

        push!(results, metrics)
    end

    return results
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS D'AFFICHAGE ET GÉNÉRATION DE RAPPORTS (IDENTIQUES À L'ORIGINAL)
# ═══════════════════════════════════════════════════════════════════════════════

"""
    generate_metrics_table_report(results::Vector{EntropyMetrics{T}}, filename::String) where T

Génère un rapport tabulaire des résultats d'analyse.
"""
function generate_metrics_table_report(
    results::Vector{EntropyMetrics{T}},
    filename::String
) where T<:AbstractFloat
    if isempty(results)
        @warn "❌ Aucun résultat à sauvegarder"
        return
    end

    try
        open(filename, "w") do file
            # En-tête du rapport
            println(file, "RAPPORT D'ANALYSE D'ENTROPIE - INDEX5")
            println(file, "=" ^ 80)
            println(file, "Nombre de positions analysées: $(length(results))")
            println(file, "=" ^ 80)
            println(file)

            # En-tête du tableau
            @printf(file, "%4s %6s %6s %12s %12s %12s %12s %12s %12s %12s %12s\n",
                "Pos", "SeqLen", "Unique", "SimpleEnt", "SimpleTheo", "CondEnt",
                "MetricEnt", "EntRate", "AEPObs", "ConfEG", "StructEG")

            # Ligne de séparation
            println(file, "-" ^ 120)

            # Données
            for result in results
                @printf(file, "%4d %6d %6d %12.6f %12.6f %12.6f %12.6f %12.6f %12.6f %12.6f %12.6f\n",
                    result.position, result.sequence_length, result.unique_values,
                    result.simple_entropy, result.simple_entropy_theoretical,
                    result.conditional_entropy, result.metric_entropy, result.entropy_rate,
                    result.entropy_aep_observed, result.conf_eg, result.struct_eg)
            end
        end

        @info "✅ Rapport sauvegardé: $filename"
    catch e
        @error "❌ Erreur lors de la sauvegarde: $e"
    end
end

"""
    display_analysis_results(results::Vector{EntropyMetrics{T}}) where T

Affiche les résultats d'analyse à l'écran.
"""
function display_analysis_results(results::Vector{EntropyMetrics{T}}) where T<:AbstractFloat
    if isempty(results)
        println("❌ Aucun résultat à afficher")
        return
    end

    println("\n" * "=" ^ 80)
    println("RÉSULTATS D'ANALYSE D'ENTROPIE - INDEX5")
    println("=" ^ 80)
    println("Nombre de positions analysées: $(length(results))")
    println("=" ^ 80)

    # Afficher les dernières valeurs (position finale)
    final_result = results[end]
    println("\n📊 VALEURS FINALES (Position $(final_result.position)):")
    println("-" ^ 60)
    @printf("🔹 Entropie Simple (Obs)      : %12.6f bits\n", final_result.simple_entropy)
    @printf("🔹 Entropie Simple (Theo)     : %12.6f bits\n", final_result.simple_entropy_theoretical)
    @printf("🔹 Entropie Conditionnelle    : %12.6f bits\n", final_result.conditional_entropy)
    @printf("🔹 Entropie Métrique          : %12.6f bits\n", final_result.metric_entropy)
    @printf("🔹 Taux d'Entropie            : %12.6f bits\n", final_result.entropy_rate)
    @printf("🔹 AEP Observé                : %12.6f bits\n", final_result.entropy_aep_observed)
    @printf("🔹 Conformité EG              : %12.6f bits\n", final_result.conf_eg)
    @printf("🔹 Structure EG               : %12.6f ratio\n", final_result.struct_eg)

    println("\n📈 ENTROPIES BLOCK:")
    for (i, block_ent) in enumerate(final_result.block_entropies)
        @printf("   Block %d: %12.6f bits\n", i, block_ent)
    end

    println("\n" * "=" ^ 80)
end

"""
    display_theoretical_probabilities(analyzer::EntropyAnalyzer{T}) where T

Affiche les probabilités théoriques INDEX5.
"""
function display_theoretical_probabilities(analyzer::EntropyAnalyzer{T}) where T<:AbstractFloat
    println("\n" * "=" ^ 60)
    println("PROBABILITÉS THÉORIQUES INDEX5")
    println("=" ^ 60)

    # Trier les probabilités par valeur décroissante
    sorted_probs = sort(collect(analyzer.theoretical_probs), by=x->x[2], rev=true)

    for (index5, prob) in sorted_probs
        @printf("%-12s : %8.6f (%.2f%%)\n", index5, prob, prob * 100)
    end

    # Vérification de la normalisation
    total = sum(values(analyzer.theoretical_probs))
    @printf("\nTotal: %.6f %s\n", total, total ≈ 1.0 ? "✅" : "❌")
    println("=" ^ 60)
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS D'ANALYSE INTERACTIVE (IDENTIQUES À L'ORIGINAL)
# ═══════════════════════════════════════════════════════════════════════════════

"""
    analyze_single_game_interactive()

Interface interactive pour analyser une partie unique.
"""
function analyze_single_game_interactive()
    println("\n🎯 ANALYSE D'UNE PARTIE UNIQUE")
    println("=" ^ 50)

    print("📁 Entrez le chemin du fichier JSON: ")
    filepath = strip(readline())

    if !isfile(filepath)
        println("❌ Fichier non trouvé: $filepath")
        return
    end

    # Charger les données
    games_data = load_baccarat_data(filepath)
    if isempty(games_data)
        println("❌ Aucune donnée chargée")
        return
    end

    # Sélectionner une partie
    println("\n📋 Parties disponibles:")
    for (i, game) in enumerate(games_data[1:min(10, length(games_data))])
        game_id = get(game, "game_id", "ID_$i")
        println("   $i. Partie $game_id")
    end

    if length(games_data) > 10
        println("   ... et $(length(games_data) - 10) autres parties")
    end

    print("\n🔢 Numéro de la partie à analyser (1-$(length(games_data))): ")
    try
        game_index = parse(Int, strip(readline()))
        if game_index < 1 || game_index > length(games_data)
            println("❌ Numéro de partie invalide")
            return
        end

        # Extraire la séquence INDEX5
        sequence = extract_index5_sequence(games_data[game_index])
        if isempty(sequence)
            println("❌ Aucune séquence INDEX5 valide trouvée")
            return
        end

        # Créer l'analyseur et calculer les métriques
        analyzer = EntropyAnalyzer{Float64}()
        println("\n⚙️  Calcul des métriques d'entropie en cours...")
        results = calculate_all_metrics_evolution(analyzer, sequence)

        # Afficher les résultats
        display_analysis_results(results)

        # Proposer la sauvegarde
        print("\n💾 Sauvegarder le rapport? (o/N): ")
        if lowercase(strip(readline())) == "o"
            game_id = get(games_data[game_index], "game_id", "partie_$game_index")
            filename = "rapport_entropie_$(game_id).txt"
            generate_metrics_table_report(results, filename)
        end

    catch e
        println("❌ Erreur: $e")
    end
end

"""
    analyze_multiple_games_interactive()

Interface interactive pour analyser plusieurs parties.
"""
function analyze_multiple_games_interactive()
    println("\n🎯 ANALYSE DE PLUSIEURS PARTIES")
    println("=" ^ 50)

    print("📁 Entrez le chemin du fichier JSON: ")
    filepath = strip(readline())

    if !isfile(filepath)
        println("❌ Fichier non trouvé: $filepath")
        return
    end

    # Charger les données
    games_data = load_baccarat_data(filepath)
    if isempty(games_data)
        println("❌ Aucune donnée chargée")
        return
    end

    print("🔢 Nombre de parties à analyser (max $(length(games_data))): ")
    try
        num_games = parse(Int, strip(readline()))
        num_games = min(num_games, length(games_data))

        if num_games <= 0
            println("❌ Nombre de parties invalide")
            return
        end

        # Créer l'analyseur
        analyzer = EntropyAnalyzer{Float64}()

        # Analyser chaque partie
        for i in 1:num_games
            println("\n⚙️  Analyse de la partie $i/$num_games...")

            # Extraire la séquence INDEX5
            sequence = extract_index5_sequence(games_data[i])
            if isempty(sequence)
                println("⚠️  Partie $i: Aucune séquence INDEX5 valide")
                continue
            end

            # Calculer les métriques
            results = calculate_all_metrics_evolution(analyzer, sequence)

            # Sauvegarder automatiquement
            game_id = get(games_data[i], "game_id", "partie_$i")
            filename = "rapport_entropie_$(game_id).txt"
            generate_metrics_table_report(results, filename)

            println("✅ Partie $i analysée et sauvegardée: $filename")
        end

        println("\n🎉 Analyse de $num_games parties terminée!")

    catch e
        println("❌ Erreur: $e")
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# INTERFACE UTILISATEUR PRINCIPALE (IDENTIQUE À L'ORIGINAL)
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main()

Interface utilisateur principale avec menu interactif.
"""
function main()
    println("🔬 ANALYSEUR D'ENTROPIE BACCARAT - INDEX5")
    println("=" ^ 60)
    println("Programme d'analyse de l'évolution de l'entropie")
    println("Basé sur 24 formules mathématiques de Formules1.txt")
    println("=" ^ 60)

    while true
        println("\n📋 MENU PRINCIPAL:")
        println("   1. 🎯 Analyser une partie unique")
        println("   2. 📊 Analyser plusieurs parties")
        println("   3. 📈 Afficher les probabilités théoriques INDEX5")
        println("   4. 🚪 Quitter")

        print("\n🔢 Votre choix (1-4): ")
        choice = strip(readline())

        if choice == "1"
            analyze_single_game_interactive()
        elseif choice == "2"
            analyze_multiple_games_interactive()
        elseif choice == "3"
            analyzer = EntropyAnalyzer{Float64}()
            display_theoretical_probabilities(analyzer)
        elseif choice == "4"
            println("\n👋 Au revoir!")
            break
        else
            println("❌ Choix invalide. Veuillez entrer un nombre entre 1 et 4.")
        end
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# POINT D'ENTRÉE PRINCIPAL (IDENTIQUE À L'ORIGINAL)
# ═══════════════════════════════════════════════════════════════════════════════

# Exécuter le programme principal si ce fichier est lancé directement
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end

"""
    calculer_formule8A_entropie_croisee_obs_theo(analyzer::FormuleAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 8A : H_cross_obs_theo(P_n, Q_n) = -∑ᵢ₌₁ⁿ p_obs(xᵢ) log₂ p_theo(xᵢ)
Entropie croisée entre probabilités observées et théoriques.
"""
function calculer_formule8A_entropie_croisee_obs_theo(
    analyzer::FormuleAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer les probabilités observées
    obs_probs = calculate_observed_probabilities(sequence)

    # Calculer l'entropie croisée
    cross_entropy = zero(T)
    for (value, p_obs) in obs_probs
        p_theo = get(analyzer.theoretical_probs, value, analyzer.epsilon)
        cross_entropy -= T(p_obs) * safe_log(p_theo, analyzer.base, analyzer.epsilon)
    end

    return cross_entropy
end

"""
    calculer_formule8B_entropie_croisee_theo_unif(analyzer::FormuleAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 8B : H_cross_theo_unif(P_n, U_n) = -∑ᵢ₌₁ⁿ p_theo(xᵢ) log₂ p_unif(xᵢ)
Entropie croisée entre probabilités théoriques INDEX5 et distribution uniforme.
"""
function calculer_formule8B_entropie_croisee_theo_unif(
    analyzer::FormuleAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    # Distribution uniforme sur 18 valeurs INDEX5
    p_unif = T(1.0 / 18.0)

    # Calculer l'entropie croisée
    cross_entropy = zero(T)
    for (_, p_theo) in analyzer.theoretical_probs
        cross_entropy -= p_theo * safe_log(p_unif, analyzer.base, analyzer.epsilon)
    end

    return cross_entropy
end

"""
    calculer_formule9A_entropie_topologique_obs(analyzer::FormuleAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 9A : h_top_obs(f) = lim_{ε→0} lim_{n→∞} (1/n) log s_n_obs(ε)
Entropie topologique avec patterns observés.
"""
function calculer_formule9A_entropie_topologique_obs(
    analyzer::FormuleAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Approximation : compter les motifs distincts observés
    unique_patterns = Set(sequence)
    s_n = length(unique_patterns)

    return safe_log(T(s_n), analyzer.base, analyzer.epsilon) / length(sequence)
end

"""
    calculer_formule9B_entropie_topologique_theo(analyzer::FormuleAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 9B : h_top_theo(f) = lim_{ε→0} lim_{n→∞} (1/n) log s_n_theo(ε)
Entropie topologique avec modèle théorique INDEX5.
"""
function calculer_formule9B_entropie_topologique_theo(
    analyzer::FormuleAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Approximation : utiliser le nombre de valeurs INDEX5 théoriques possibles
    s_n_theo = length(analyzer.theoretical_probs)

    return safe_log(T(s_n_theo), analyzer.base, analyzer.epsilon) / length(sequence)
end

# ─────────────────────────────────────────────────────────────────────────────
# FORMULES SPÉCIALISÉES (10-12) - 6 FONCTIONS
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculer_formule10A_block_cumulative_obs(analyzer::FormuleAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 10A : H_block_obs(X₁ⁿ) = (1/n) ∑ₖ₌₁ⁿ H_obs(X₁ᵏ)
Entropie block cumulative avec probabilités observées.
"""
function calculer_formule10A_block_cumulative_obs(
    analyzer::FormuleAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer la somme des entropies pour chaque préfixe
    total_entropy = zero(T)
    for k in 1:length(sequence)
        prefix = sequence[1:k]
        total_entropy += calculer_formule1A_shannon_jointe_obs(analyzer, prefix)
    end

    return total_entropy / length(sequence)
end

"""
    calculer_formule10B_block_cumulative_theo(analyzer::FormuleAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 10B : H_block_theo(X₁ⁿ) = (1/n) ∑ₖ₌₁ⁿ H_theo(X₁ᵏ)
Entropie block cumulative avec probabilités théoriques INDEX5.
"""
function calculer_formule10B_block_cumulative_theo(
    analyzer::FormuleAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer la somme des entropies théoriques pour chaque préfixe
    total_entropy = zero(T)
    for k in 1:length(sequence)
        prefix = sequence[1:k]
        total_entropy += calculer_formule1B_shannon_jointe_theo(analyzer, prefix)
    end

    return total_entropy / length(sequence)
end

"""
    calculer_formule11A_conditionnelle_decroissante_obs(analyzer::FormuleAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 11A : H_dec_obs(X₁ⁿ) = ∑ₖ₌₁ⁿ (1/k) H_obs(Xₖ|X₁ᵏ⁻¹)
Entropie conditionnelle décroissante avec probabilités observées.
"""
function calculer_formule11A_conditionnelle_decroissante_obs(
    analyzer::FormuleAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer la somme pondérée des entropies conditionnelles
    total_weighted_entropy = zero(T)
    for k in 1:length(sequence)
        if k == 1
            # H_obs(X₁) = H_obs(X₁|∅)
            h_cond = calculer_formule1A_shannon_jointe_obs(analyzer, sequence[1:1])
        else
            # H_obs(Xₖ|X₁ᵏ⁻¹) approximé par différence d'entropies
            h_k = calculer_formule1A_shannon_jointe_obs(analyzer, sequence[1:k])
            h_k_minus_1 = calculer_formule1A_shannon_jointe_obs(analyzer, sequence[1:k-1])
            h_cond = h_k - h_k_minus_1
        end
        total_weighted_entropy += h_cond / k
    end

    return total_weighted_entropy
end

"""
    calculer_formule11B_conditionnelle_decroissante_theo(analyzer::FormuleAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 11B : H_dec_theo(X₁ⁿ) = ∑ₖ₌₁ⁿ (1/k) H_theo(Xₖ|X₁ᵏ⁻¹)
Entropie conditionnelle décroissante avec probabilités théoriques INDEX5.
"""
function calculer_formule11B_conditionnelle_decroissante_theo(
    analyzer::FormuleAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer la somme pondérée des entropies conditionnelles théoriques
    total_weighted_entropy = zero(T)
    for k in 1:length(sequence)
        if k == 1
            # H_theo(X₁) = H_theo(X₁|∅)
            h_cond = calculer_formule1B_shannon_jointe_theo(analyzer, sequence[1:1])
        else
            # H_theo(Xₖ|X₁ᵏ⁻¹) approximé par différence d'entropies
            h_k = calculer_formule1B_shannon_jointe_theo(analyzer, sequence[1:k])
            h_k_minus_1 = calculer_formule1B_shannon_jointe_theo(analyzer, sequence[1:k-1])
            h_cond = h_k - h_k_minus_1
        end
        total_weighted_entropy += h_cond / k
    end

    return total_weighted_entropy
end

"""
    calculer_formule12A_theoreme_aep_obs(analyzer::FormuleAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 12A : AEP_obs(X₁ⁿ) = -(1/n) log₂ ∏ᵢ₌₁ⁿ p_obs(xᵢ)
Théorème AEP (Shannon-McMillan-Breiman) avec probabilités observées.
"""
function calculer_formule12A_theoreme_aep_obs(
    analyzer::FormuleAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer les probabilités observées
    obs_probs = calculate_observed_probabilities(sequence)

    # Calculer le produit des probabilités (en log pour éviter l'underflow)
    total_log_prob = zero(T)
    for value in sequence
        p_obs = T(get(obs_probs, value, 0.0))
        total_log_prob += safe_log(p_obs, analyzer.base, analyzer.epsilon)
    end

    return -total_log_prob / length(sequence)
end

"""
    calculer_formule12B_theoreme_aep_theo(analyzer::FormuleAnalyzer{T}, sequence::Vector{String}) where T -> T

Formule 12B : AEP_theo(X₁ⁿ) = -(1/n) log₂ ∏ᵢ₌₁ⁿ p_theo(xᵢ)
Théorème AEP (Shannon-McMillan-Breiman) avec probabilités théoriques INDEX5.
"""
function calculer_formule12B_theoreme_aep_theo(
    analyzer::FormuleAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return zero(T)
    end

    # Calculer le produit des probabilités théoriques (en log pour éviter l'underflow)
    total_log_prob = zero(T)
    for value in sequence
        p_theo = get(analyzer.theoretical_probs, value, analyzer.epsilon)
        total_log_prob += safe_log(p_theo, analyzer.base, analyzer.epsilon)
    end

    return -total_log_prob / length(sequence)
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE DE CALCUL
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculate_all_formules_evolution(analyzer::FormuleAnalyzer{T}, sequence::Vector{String}) where T -> Vector{FormuleMetrics{T}}

Calcule l'évolution des 24 formules position par position dans la séquence.
"""
function calculate_all_formules_evolution(
    analyzer::FormuleAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    if isempty(sequence)
        return FormuleMetrics{T}[]
    end

    results = FormuleMetrics{T}[]

    # Calculer les métriques pour chaque position
    for n in 1:length(sequence)
        current_sequence = sequence[1:n]

        # Calculer toutes les 24 formules
        metrics = FormuleMetrics{T}(
            n,
            # Formules fondamentales (1-5)
            calculer_formule1A_shannon_jointe_obs(analyzer, current_sequence),
            calculer_formule1B_shannon_jointe_theo(analyzer, current_sequence),
            calculer_formule2A_aep_obs(analyzer, current_sequence),
            calculer_formule2B_aep_theo(analyzer, current_sequence),
            calculer_formule3A_taux_entropie_obs(analyzer, current_sequence),
            calculer_formule3B_taux_entropie_theo(analyzer, current_sequence),
            calculer_formule4A_entropie_metrique_obs(analyzer, current_sequence),
            calculer_formule4B_entropie_metrique_theo(analyzer, current_sequence),
            calculer_formule5A_conditionnelle_obs(analyzer, current_sequence),
            calculer_formule5B_conditionnelle_theo(analyzer, current_sequence),

            # Formules avancées (6-9)
            calculer_formule6A_divergence_kl_obs_theo(analyzer, current_sequence),
            calculer_formule6B_divergence_kl_theo_unif(analyzer, current_sequence),
            calculer_formule7A_information_mutuelle_obs(analyzer, current_sequence),
            calculer_formule7B_information_mutuelle_theo(analyzer, current_sequence),
            calculer_formule8A_entropie_croisee_obs_theo(analyzer, current_sequence),
            calculer_formule8B_entropie_croisee_theo_unif(analyzer, current_sequence),
            calculer_formule9A_entropie_topologique_obs(analyzer, current_sequence),
            calculer_formule9B_entropie_topologique_theo(analyzer, current_sequence),

            # Formules spécialisées (10-12)
            calculer_formule10A_block_cumulative_obs(analyzer, current_sequence),
            calculer_formule10B_block_cumulative_theo(analyzer, current_sequence),
            calculer_formule11A_conditionnelle_decroissante_obs(analyzer, current_sequence),
            calculer_formule11B_conditionnelle_decroissante_theo(analyzer, current_sequence),
            calculer_formule12A_theoreme_aep_obs(analyzer, current_sequence),
            calculer_formule12B_theoreme_aep_theo(analyzer, current_sequence)
        )

        push!(results, metrics)
    end

    return results
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS D'AFFICHAGE ET GÉNÉRATION DE RAPPORTS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    generate_formules_table_report(results::Vector{FormuleMetrics{T}}, filename::String) where T

Génère un rapport tabulaire des résultats des 24 formules.
"""
function generate_formules_table_report(
    results::Vector{FormuleMetrics{T}},
    filename::String
) where T<:AbstractFloat
    if isempty(results)
        @warn "❌ Aucun résultat à sauvegarder"
        return
    end

    try
        open(filename, "w") do file
            # En-tête du rapport
            println(file, "RAPPORT D'ANALYSE - 24 FORMULES MATHÉMATIQUES")
            println(file, "=" ^ 80)
            println(file, "Nombre de positions analysées: $(length(results))")
            println(file, "Date de génération: $(now())")
            println(file, "=" ^ 80)
            println(file)

            # En-tête du tableau
            @printf(file, "%4s", "Pos")

            # Formules fondamentales
            @printf(file, " %12s %12s %12s %12s", "F1A_Shan_O", "F1B_Shan_T", "F2A_AEP_O", "F2B_AEP_T")
            @printf(file, " %12s %12s %12s %12s", "F3A_Taux_O", "F3B_Taux_T", "F4A_Metr_O", "F4B_Metr_T")
            @printf(file, " %12s %12s", "F5A_Cond_O", "F5B_Cond_T")

            # Formules avancées
            @printf(file, " %12s %12s %12s %12s", "F6A_KL_OT", "F6B_KL_TU", "F7A_MI_O", "F7B_MI_T")
            @printf(file, " %12s %12s %12s %12s", "F8A_CE_OT", "F8B_CE_TU", "F9A_Top_O", "F9B_Top_T")

            # Formules spécialisées
            @printf(file, " %12s %12s %12s %12s", "F10A_Blk_O", "F10B_Blk_T", "F11A_Dec_O", "F11B_Dec_T")
            @printf(file, " %12s %12s", "F12A_AEP_O", "F12B_AEP_T")
            println(file)

            # Ligne de séparation
            println(file, "-" ^ 320)

            # Données
            for result in results
                @printf(file, "%4d", result.position)

                # Formules fondamentales
                @printf(file, " %12.6f %12.6f %12.6f %12.6f",
                    result.formule1A_shannon_jointe_obs, result.formule1B_shannon_jointe_theo,
                    result.formule2A_aep_obs, result.formule2B_aep_theo)
                @printf(file, " %12.6f %12.6f %12.6f %12.6f",
                    result.formule3A_taux_entropie_obs, result.formule3B_taux_entropie_theo,
                    result.formule4A_entropie_metrique_obs, result.formule4B_entropie_metrique_theo)
                @printf(file, " %12.6f %12.6f",
                    result.formule5A_conditionnelle_obs, result.formule5B_conditionnelle_theo)

                # Formules avancées
                @printf(file, " %12.6f %12.6f %12.6f %12.6f",
                    result.formule6A_divergence_kl_obs_theo, result.formule6B_divergence_kl_theo_unif,
                    result.formule7A_information_mutuelle_obs, result.formule7B_information_mutuelle_theo)
                @printf(file, " %12.6f %12.6f %12.6f %12.6f",
                    result.formule8A_entropie_croisee_obs_theo, result.formule8B_entropie_croisee_theo_unif,
                    result.formule9A_entropie_topologique_obs, result.formule9B_entropie_topologique_theo)

                # Formules spécialisées
                @printf(file, " %12.6f %12.6f %12.6f %12.6f",
                    result.formule10A_block_cumulative_obs, result.formule10B_block_cumulative_theo,
                    result.formule11A_conditionnelle_decroissante_obs, result.formule11B_conditionnelle_decroissante_theo)
                @printf(file, " %12.6f %12.6f",
                    result.formule12A_theoreme_aep_obs, result.formule12B_theoreme_aep_theo)
                println(file)
            end
        end

        @info "✅ Rapport sauvegardé: $filename"
    catch e
        @error "❌ Erreur lors de la sauvegarde: $e"
    end
end

"""
    display_analysis_results(results::Vector{FormuleMetrics{T}}) where T

Affiche les résultats d'analyse des 24 formules à l'écran.
"""
function display_analysis_results(results::Vector{FormuleMetrics{T}}) where T<:AbstractFloat
    if isempty(results)
        println("❌ Aucun résultat à afficher")
        return
    end

    println("\n" * "=" ^ 80)
    println("RÉSULTATS D'ANALYSE - 24 FORMULES MATHÉMATIQUES")
    println("=" ^ 80)
    println("Nombre de positions analysées: $(length(results))")
    println("=" ^ 80)

    # Afficher les dernières valeurs (position finale)
    final_result = results[end]
    println("\n📊 VALEURS FINALES (Position $(final_result.position)):")
    println("-" ^ 60)

    # Formules fondamentales
    println("🔹 FORMULES FONDAMENTALES:")
    @printf("   F1A - Shannon Jointe (Obs)     : %12.6f bits\n", final_result.formule1A_shannon_jointe_obs)
    @printf("   F1B - Shannon Jointe (Theo)    : %12.6f bits\n", final_result.formule1B_shannon_jointe_theo)
    @printf("   F2A - AEP (Obs)                : %12.6f bits\n", final_result.formule2A_aep_obs)
    @printf("   F2B - AEP (Theo)               : %12.6f bits\n", final_result.formule2B_aep_theo)
    @printf("   F3A - Taux Entropie (Obs)      : %12.6f bits\n", final_result.formule3A_taux_entropie_obs)
    @printf("   F3B - Taux Entropie (Theo)     : %12.6f bits\n", final_result.formule3B_taux_entropie_theo)
    @printf("   F4A - Entropie Métrique (Obs)  : %12.6f bits\n", final_result.formule4A_entropie_metrique_obs)
    @printf("   F4B - Entropie Métrique (Theo) : %12.6f bits\n", final_result.formule4B_entropie_metrique_theo)
    @printf("   F5A - Conditionnelle (Obs)     : %12.6f bits\n", final_result.formule5A_conditionnelle_obs)
    @printf("   F5B - Conditionnelle (Theo)    : %12.6f bits\n", final_result.formule5B_conditionnelle_theo)

    # Formules avancées
    println("\n🔹 FORMULES AVANCÉES:")
    @printf("   F6A - Divergence KL (Obs→Theo) : %12.6f bits\n", final_result.formule6A_divergence_kl_obs_theo)
    @printf("   F6B - Divergence KL (Theo→Unif): %12.6f bits\n", final_result.formule6B_divergence_kl_theo_unif)
    @printf("   F7A - Information Mutuelle (O) : %12.6f bits\n", final_result.formule7A_information_mutuelle_obs)
    @printf("   F7B - Information Mutuelle (T) : %12.6f bits\n", final_result.formule7B_information_mutuelle_theo)
    @printf("   F8A - Entropie Croisée (O→T)   : %12.6f bits\n", final_result.formule8A_entropie_croisee_obs_theo)
    @printf("   F8B - Entropie Croisée (T→U)   : %12.6f bits\n", final_result.formule8B_entropie_croisee_theo_unif)
    @printf("   F9A - Entropie Topologique (O) : %12.6f bits\n", final_result.formule9A_entropie_topologique_obs)
    @printf("   F9B - Entropie Topologique (T) : %12.6f bits\n", final_result.formule9B_entropie_topologique_theo)

    # Formules spécialisées
    println("\n🔹 FORMULES SPÉCIALISÉES:")
    @printf("   F10A - Block Cumulative (Obs)  : %12.6f bits\n", final_result.formule10A_block_cumulative_obs)
    @printf("   F10B - Block Cumulative (Theo) : %12.6f bits\n", final_result.formule10B_block_cumulative_theo)
    @printf("   F11A - Cond. Décroissante (O)  : %12.6f bits\n", final_result.formule11A_conditionnelle_decroissante_obs)
    @printf("   F11B - Cond. Décroissante (T)  : %12.6f bits\n", final_result.formule11B_conditionnelle_decroissante_theo)
    @printf("   F12A - Théorème AEP (Obs)      : %12.6f bits\n", final_result.formule12A_theoreme_aep_obs)
    @printf("   F12B - Théorème AEP (Theo)     : %12.6f bits\n", final_result.formule12B_theoreme_aep_theo)

    println("\n" * "=" ^ 80)
end

"""
    display_theoretical_probabilities(analyzer::FormuleAnalyzer{T}) where T

Affiche les probabilités théoriques INDEX5.
"""
function display_theoretical_probabilities(analyzer::FormuleAnalyzer{T}) where T<:AbstractFloat
    println("\n" * "=" ^ 60)
    println("PROBABILITÉS THÉORIQUES INDEX5")
    println("=" ^ 60)

    # Trier les probabilités par valeur décroissante
    sorted_probs = sort(collect(analyzer.theoretical_probs), by=x->x[2], rev=true)

    for (index5, prob) in sorted_probs
        @printf("%-12s : %8.6f (%.2f%%)\n", index5, prob, prob * 100)
    end

    # Vérification de la normalisation
    total = sum(values(analyzer.theoretical_probs))
    @printf("\nTotal: %.6f %s\n", total, total ≈ 1.0 ? "✅" : "❌")
    println("=" ^ 60)
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS D'ANALYSE INTERACTIVE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    analyze_single_game_interactive()

Interface interactive pour analyser une partie unique.
"""
function analyze_single_game_interactive()
    println("\n🎯 ANALYSE D'UNE PARTIE UNIQUE")
    println("=" ^ 50)

    print("📁 Entrez le chemin du fichier JSON: ")
    filepath = strip(readline())

    if !isfile(filepath)
        println("❌ Fichier non trouvé: $filepath")
        return
    end

    # Charger les données
    games_data = load_baccarat_data(filepath)
    if isempty(games_data)
        println("❌ Aucune donnée chargée")
        return
    end

    # Sélectionner une partie
    println("\n📋 Parties disponibles:")
    for (i, game) in enumerate(games_data[1:min(10, length(games_data))])
        game_id = get(game, "game_id", "ID_$i")
        println("   $i. Partie $game_id")
    end

    if length(games_data) > 10
        println("   ... et $(length(games_data) - 10) autres parties")
    end

    print("\n🔢 Numéro de la partie à analyser (1-$(length(games_data))): ")
    try
        game_index = parse(Int, strip(readline()))
        if game_index < 1 || game_index > length(games_data)
            println("❌ Numéro de partie invalide")
            return
        end

        # Extraire la séquence INDEX5
        sequence = extract_index5_sequence(games_data[game_index])
        if isempty(sequence)
            println("❌ Aucune séquence INDEX5 valide trouvée")
            return
        end

        # Créer l'analyseur et calculer les métriques
        analyzer = FormuleAnalyzer{Float64}()
        println("\n⚙️  Calcul des 24 formules en cours...")
        results = calculate_all_formules_evolution(analyzer, sequence)

        # Afficher les résultats
        display_analysis_results(results)

        # Proposer la sauvegarde
        print("\n💾 Sauvegarder le rapport? (o/N): ")
        if lowercase(strip(readline())) == "o"
            game_id = get(games_data[game_index], "game_id", "partie_$game_index")
            filename = "rapport_formules_$(game_id)_$(now()).txt"
            generate_formules_table_report(results, filename)
        end

    catch e
        println("❌ Erreur: $e")
    end
end

"""
    analyze_multiple_games_interactive()

Interface interactive pour analyser plusieurs parties.
"""
function analyze_multiple_games_interactive()
    println("\n🎯 ANALYSE DE PLUSIEURS PARTIES")
    println("=" ^ 50)

    print("📁 Entrez le chemin du fichier JSON: ")
    filepath = strip(readline())

    if !isfile(filepath)
        println("❌ Fichier non trouvé: $filepath")
        return
    end

    # Charger les données
    games_data = load_baccarat_data(filepath)
    if isempty(games_data)
        println("❌ Aucune donnée chargée")
        return
    end

    print("🔢 Nombre de parties à analyser (max $(length(games_data))): ")
    try
        num_games = parse(Int, strip(readline()))
        num_games = min(num_games, length(games_data))

        if num_games <= 0
            println("❌ Nombre de parties invalide")
            return
        end

        # Créer l'analyseur
        analyzer = FormuleAnalyzer{Float64}()

        # Analyser chaque partie
        for i in 1:num_games
            println("\n⚙️  Analyse de la partie $i/$num_games...")

            # Extraire la séquence INDEX5
            sequence = extract_index5_sequence(games_data[i])
            if isempty(sequence)
                println("⚠️  Partie $i: Aucune séquence INDEX5 valide")
                continue
            end

            # Calculer les métriques
            results = calculate_all_formules_evolution(analyzer, sequence)

            # Sauvegarder automatiquement
            game_id = get(games_data[i], "game_id", "partie_$i")
            filename = "rapport_formules_$(game_id).txt"
            generate_formules_table_report(results, filename)

            println("✅ Partie $i analysée et sauvegardée: $filename")
        end

        println("\n🎉 Analyse de $num_games parties terminée!")

    catch e
        println("❌ Erreur: $e")
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# INTERFACE UTILISATEUR PRINCIPALE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main()

Interface utilisateur principale avec menu interactif.
"""
function main()
    println("🔬 ANALYSEUR DE FORMULES MATHÉMATIQUES - INDEX5")
    println("=" ^ 60)
    println("Programme d'analyse basé sur 24 formules mathématiques")
    println("Architecture Julia avec types paramétriques et dispatch multiple")
    println("=" ^ 60)

    while true
        println("\n📋 MENU PRINCIPAL:")
        println("   1. 🎯 Analyser une partie unique")
        println("   2. 📊 Analyser plusieurs parties")
        println("   3. 📈 Afficher les probabilités théoriques INDEX5")
        println("   4. ❓ Aide et informations")
        println("   5. 🚪 Quitter")

        print("\n🔢 Votre choix (1-5): ")
        choice = strip(readline())

        if choice == "1"
            analyze_single_game_interactive()
        elseif choice == "2"
            analyze_multiple_games_interactive()
        elseif choice == "3"
            analyzer = FormuleAnalyzer{Float64}()
            display_theoretical_probabilities(analyzer)
        elseif choice == "4"
            display_help()
        elseif choice == "5"
            println("\n👋 Au revoir!")
            break
        else
            println("❌ Choix invalide. Veuillez entrer un nombre entre 1 et 5.")
        end
    end
end

"""
    display_help()

Affiche l'aide et les informations sur le programme.
"""
function display_help()
    println("\n" * "=" ^ 70)
    println("AIDE - ANALYSEUR DE FORMULES MATHÉMATIQUES")
    println("=" ^ 70)

    println("\n🎯 OBJECTIF:")
    println("   Ce programme implémente 24 formules mathématiques basées sur")
    println("   la théorie de l'information et l'entropie pour analyser les")
    println("   séquences INDEX5 du baccarat.")

    println("\n📊 FORMULES IMPLÉMENTÉES:")
    println("   🔹 Formules Fondamentales (F1-F5): 10 fonctions")
    println("      • Entropie de Shannon jointe (observée/théorique)")
    println("      • Entropie AEP (Asymptotic Equipartition Property)")
    println("      • Taux d'entropie (Entropy Rate)")
    println("      • Entropie métrique (Kolmogorov-Sinai)")
    println("      • Entropie conditionnelle cumulative")

    println("\n   🔹 Formules Avancées (F6-F9): 8 fonctions")
    println("      • Divergence KL (Entropie Relative)")
    println("      • Information mutuelle")
    println("      • Entropie croisée")
    println("      • Entropie topologique")

    println("\n   🔹 Formules Spécialisées (F10-F12): 6 fonctions")
    println("      • Entropie block cumulative")
    println("      • Entropie conditionnelle décroissante")
    println("      • Théorème AEP (Shannon-McMillan-Breiman)")

    println("\n⚙️  ARCHITECTURE TECHNIQUE:")
    println("   • Types paramétriques {T<:AbstractFloat}")
    println("   • Dispatch multiple pour optimisation automatique")
    println("   • Validation multi-niveaux des données")
    println("   • Gestion robuste des cas limites")
    println("   • Probabilités théoriques INDEX5 intégrées")

    println("\n📁 FORMAT DES DONNÉES:")
    println("   • Fichiers JSON avec structure 'parties_condensees'")
    println("   • Support des formats 'hands' et 'mains_condensees'")
    println("   • Filtrage automatique des mains d'ajustement")
    println("   • Extraction et validation des séquences INDEX5")

    println("\n📈 RÉSULTATS:")
    println("   • Calcul position par position dans chaque séquence")
    println("   • Affichage formaté avec 6 décimales de précision")
    println("   • Génération automatique de rapports tabulaires")
    println("   • Sauvegarde en fichiers texte horodatés")

    println("\n" * "=" ^ 70)
end

# ═══════════════════════════════════════════════════════════════════════════════
# POINT D'ENTRÉE PRINCIPAL
# ═══════════════════════════════════════════════════════════════════════════════

# Exécuter le programme principal si ce fichier est lancé directement
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end
